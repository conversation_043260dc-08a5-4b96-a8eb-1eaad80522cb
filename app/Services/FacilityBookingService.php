<?php

namespace App\Services\OpenApi\Free;

use App\Models\CustomerDocument;
use App\Models\FacilityBookingDocuments;
use App\Models\VenueService;
use App\Services\PDF\PdfGeneratorService;
class FacilityBookingService
{


    public function saveDisclaimers($customer, $venueServiceId, $venueId, $facilityBooking, $fac): void
    {
        if (isset($customer->disclaimers) && is_array($customer->disclaimers) && count($customer->disclaimers) > 0) {
            $service = VenueService::join('services as s', 's.id', 'venue_services.service_id')
                ->where('venue_services.id', $venueServiceId)
                ->select('s.name')
                ->first();
            $document = CustomerDocument::where('customer_id', $customer->id)
                ->whereIn('id_proof_type_id', [32, 33])
                ->orderBy('id_proof_type_id')
                ->first();
            foreach ($customer->disclaimers as $disclaimer) {
                $pdfSvc = new PdfGeneratorService();
                $facilityName = isset($fac) ? $fac->name : '';
                $bookingDate = $facilityBooking->date;
                $data = (object)[
                    'facility_name' => $facilityName,
                    'customer_name' => $customer->first_name . ' ' . $customer->last_name,
                    'booking_date' => $bookingDate,
                    'service_name' => $service?->name,
                    'dob' => $customer->dob,
                    'id_proof_number' => $document?->id_proof_number,
                    'booking_time' => $facilityBooking->start_time,
                ];

                $path = $pdfSvc->generateDisclaimerPdf($disclaimer, $data, $venueId, $disclaimer->value);
                $facilityBookingDocuments = new FacilityBookingDocuments();
                $facilityBookingDocuments->facility_booking_id = $facilityBooking->id;
                $facilityBookingDocuments->document_name = $disclaimer->name;
                $facilityBookingDocuments->original_file_name = $disclaimer->name;
                $facilityBookingDocuments->document_path = $path;
                $facilityBookingDocuments->status_id = 1;
                $facilityBookingDocuments->save();

                $customerDocument = new CustomerDocument();
                $customerDocument->venue_id = $venueId;
                $customerDocument->customer_id = $customer->id;;
                $customerDocument->id_proof_type_id = 44;
                $customerDocument->id_proof_path = $path;
                $customerDocument->document_name = $facilityName . ' ' . $bookingDate;;
                $customerDocument->save();
            }
        }
    }
}
