<?php

namespace App\Services\OpenApi\Free;

use App\Helper\DiscountHelper;
use App\Helper\FacilityBookingHelper;
use App\Helper\FacilityHelper;
use App\Helper\MailHelper;
use App\Helper\OrderHelper;
use App\Http\Controllers\Controller;
use App\Mail\FacilityBookingThresholdEmail;
use App\Models\CustomerConfigurations;
use App\Models\CustomerDocument;
use App\Models\Facility;
use App\Models\FacilityBooking;
use App\Models\FacilityBookingDocuments;
use App\Models\FacilityBookingItem;
use App\Models\FacilityThresholdEmailLog;
use App\Models\GroupCustomer;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\Product;
use App\Models\VenueService;
use App\Services\PDF\PdfGeneratorService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use PHPUnit\Util\Exception;
use RuntimeException;

class FreeFacilityBookingService
{
    private Controller $controller;
    public function __construct()
    {
        $this->controller = new Controller();
    }
    /**
     * Store the orders' data.
     *
     * @param object $data
     * @return array<Order>
     * @throws RuntimeException|\Exception
     */
    public function store($request): array
    {
        $products = collect($request->products);
        $haveRentals = $products->where('rental', true)->count() > 0;

        if (!$haveRentals) {
            throw new RuntimeException('Please select at least one Ticket');
        }

        $facility = Facility::where(['venue_id' => $request->venueId,'status_id' => 1,'is_public' => 1])->first();
        //$facilityAvailability = FacilityHelper::facilityAvailability($data, $data->venue_service_id, $data->venueId);
        if (! $facility) {
            throw new RuntimeException('Facility not available');
        }
        $this->controller->venueId=$request->venueId;
        return [$this->saveBookingOrder($request,$facility)];
    }
    public function hasRentalProduct($array): bool
    {
        foreach ($array as $child) {
            if ($child['product_id'] !== '0' || is_null($child['product_id'])) {
                return true;
            }
        }
        return false;
    }

    /**
     * @throws \Exception
     */
    private function createCustomer($request, $venueId)
    {
        $this->controller->venueId = $venueId; // handle multi-venue scenario
        $customer = $this->controller->createOrUpdateCustomer($request);
        $this->controller->createVenueCustomer($customer, 'Website', $venueId);
        return $customer;
    }

    private function createOrder(&$data, $customerId)
    {
        $totalPrice = 0;
        $totalTax = 0;
        $totalAmount = 0;
        $order_datetime = $data->date . " 00:00:00";
        if ($data->start_time && $data->date) {
            $order_datetime = date("Y-m-d H:i", strtotime($data->date . ' ' . $data->start_time));
        }
        $order = OrderHelper::createOrUpdateOrder([
            'order_date' => $data->date,
            'order_datetime' => $order_datetime,
            'order_notes' => null,
            'invoice_seq_no' => $this->controller->generateSequence('O'),
            'price' => 0,
            'tax' => 0,
            'total' => 0,
            'status_id' => 5,
            'order_status_id' => 11,
            'venue_id' => $data->venueId,
            'customer_id' => $customerId,
            'initial_order_id' => null,
            'invoice_generated' => 0
        ]);

        if ($data->products) {
            $products = collect($data->products);
            foreach ($products as $key => $item) {
                $item = (object)$item;
                $product = null;
                if (isset($item->product_id) && $item->product_id > 0) {
                    $product = Product::where(['id' => $item->product_id, 'venue_id' => (int)$data->venueId, 'status' => 1])
                        ->select([
                            'products.*',
                            DB::raw('products.tax_amount '),
                            DB::raw('products.price'),
                        ])->first();
                    if (!$product) {
                        throw new RuntimeException("Incorrect Venue Id with product");
                    }
                } else {
                    throw new RuntimeException("Product Id required");
                }
                $orderItem = NULL;

                $quantity = $item->quantity;
                $price = $product->price * $quantity;
                $tax = $product->tax_amount * $quantity;
                $total = $price + $tax;

                $totalPrice += $price;
                $totalTax += $tax;
                $totalAmount += $total;
                $orderItem = OrderHelper::createOrUpdateOrderItem([
                    'order_id' => $order->id,
                    'price' => $price,
                    'tax' => $tax,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'status_id' => 5,
                    'total' => $total,
                    'venue_service_id' => $data->venue_service_id
                ]);
            }
        }else{
            throw new RuntimeException("Products required");
        }

        $order->price = $totalPrice;
        $order->tax = $totalTax;
        $order->total = $totalAmount;
        $order->save();
        return $order;
    }


    private function createFacilityBooking($data, $order, $facility, $participants = 1): FacilityBooking
    {
        $facilityBooking = new FacilityBooking;
        $facilityBooking->date = $data->date;
        $facilityBooking->start_time = $data->start_time;
        $facilityBooking->end_time = $data->end_time;
        $facilityBooking->booking_end_date = FacilityBookingHelper::getFacilityBookingEndDate($data->start_time, $data->end_time, $data->date);
        $facilityBooking->facility_id = $data->facility_id;
        if ($facility && $facility->is_enable_booking_approval) {
            $facilityBooking->is_booking_approved = 0;
        }
        $facilityBooking->attendance = $participants;
        if (isset($data->duration)) {
            $facilityBooking->duration = $data->duration;
        }
        $facilityBooking->status_id = $order->status_id; // Unpaid
        $facilityBooking->order_id = $order->id;
        $facilityBooking->save();
        return $facilityBooking;
    }

    /**
     * @throws \Exception
     */
    private function saveBookingOrder($data, $facility): Order
    {
        $facilityId = $data->facility_id;
        $perCapacity = $facility->per_capacity;
        $mainCustomer = $data->customers[0];
        $customer = $this->createCustomer($mainCustomer, $data->venueId);
        $order = $this->createOrder($data, $customer->id);
        $products = collect($data->products)->sortBy('product_id');
        $orderItems = OrderItem::where('order_id', $order->id)->where('status_id', '!=', 2)->get();
        $productIds = [];
        $participants = 0;
        foreach($orderItems as $item){
            // it should be rental product ids not addons
            array_push($productIds, $item->product_id);
            $participants += $item->quantity;
        }
        $data->duration = $facility->min_booking_time;
        $facilityBooking = $this->createFacilityBooking($data, $order, $facility,$participants);
        if ($facility) {
            $facilityThresholdEmailLog = null;
            if ($facility->is_enable_per_day_capacity == 0) {
                $total_attendance = FacilityBooking::where('facility_id', $facilityId)
                    ->where('start_time', $data->start_time)
                    ->where('end_time', $data->end_time)
                    ->where('date', $data->date)
                    ->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7]
                    )->sum('attendance');

                if($facility->is_enable_email_threshold == 1 && $facility->online_threshold > 0 && $facility->email_threshold_recipient != "" && $total_attendance >= $facility->online_threshold){
                    $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)
                        ->where('start_time', $data->start_time)
                        ->where('end_time', $data->end_time)
                        ->where('date', $data->date)->first();
                }
            } else {
                $total_attendance = FacilityBooking::where('facility_id', $facilityId)
                    ->where('date', $data->date)
                    ->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7])->sum('attendance');
                if($facility->is_enable_email_threshold == 1 && $facility->online_threshold > 0 && $facility->email_threshold_recipient != "" && $total_attendance >= $facility->online_threshold) {
                    $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)->where('date', $data->date)->first();
                }
            }
            if ($facility->capacity && $total_attendance > $facility->capacity) {
                throw new RuntimeException('Trying to book more than available capacity');
            }

            if ($perCapacity) {
                $thresholdCheck = $this->checkCustomerBookingThreshold($data, $facility, $customer);
                if (!$thresholdCheck) {
                    throw new RuntimeException('You Are Exceeding Maximum Allowed Tickets');
                }
            }
            /** send email threshold */
            if (!$facilityThresholdEmailLog && $facility && $facility->is_enable_email_threshold == 1 && $facility->online_threshold > 0 && $facility->email_threshold_recipient != "" && $total_attendance >= $facility->online_threshold) {

                $facilityThresholdEmailLog = new FacilityThresholdEmailLog();
                $facilityThresholdEmailLog->facility_id = $facilityId;
                $facilityThresholdEmailLog->date = $data->date;
                $facilityThresholdEmailLog->start_time = $data->start_time;
                $facilityThresholdEmailLog->end_time = $data->end_time;
                $facilityThresholdEmailLog->save();
                $result = MailHelper::sendEmail($facility->email_threshold_recipient, new FacilityBookingThresholdEmail($facility, $facilityThresholdEmailLog, $customer, $data->venueId));
            }
            /** end email threshold */
        }
        foreach ($orderItems as $item) {
            $facilityBookingItem = FacilityBookingItem::updateOrCreate(['order_item_id' => $item->id, 'facility_booking_id' => $facilityBooking->id]);
            $facilityBookingItem->quantity = $item->quantity;
            if ($item->price > 0) {
                $facilityBookingItem->price = (float)$item->price;
            }
            if ($item->total > 0) {
                $facilityBookingItem->total = (float)$item->total;
            }
            if ($item->tax > 0) {
                $facilityBookingItem->tax = (float)$item->tax;
            }
            $facilityBookingItem->save();
        }
        if (isset($facilityBooking) && $facilityBooking->is_open_dated === 1) {
            $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id, 'facility_booking_id' => $facilityBooking->id]);
        } else {
            $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id]);
        }

        if (count($data->customers) > 1) {
            foreach ($data->customers as $addOns) {
                $data_for_request = (array)$addOns;
                $myRequest = new Request($data_for_request);
                $addOns = $this->createCustomer($myRequest,$data->venueId);

                $addOnOrderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'customer_id' => $addOns->id, 'status_id' => 1])->first();
                if ($addOnOrderGroupCustomer !== null) {
                    $addOnOrderGroupCustomer->customer_id = $addOns->id;
                    $addOnOrderGroupCustomer->order_id = $order->id;
                } else if (isset($facilityBooking) && $facilityBooking->is_open_dated == 1) {
                    $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id, 'facility_booking_id' => $facilityBooking->id]);
                } else {
                    $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id]);
                }
                $addOnOrderGroupCustomer->save();
            }
        }
        return $order;
    }

    private function checkCustomerBookingThreshold($data, $fac, $customer): bool
    {
        $threshold = CustomerConfigurations::where('venue_id', $data->venueId)->where('status_id', 1)->first();
        if ($threshold && !is_null($threshold->customer_threshold)) {
            if ($fac->is_enable_per_day_capacity === 0) {
                $orders = DB::table('orders as o')
                    ->join('facility_bookings as fb', 'fb.order_id', '=', 'o.id')
                    ->where('o.customer_id', $customer->id)
                    ->where('o.venue_id', $data->venueId)
                    ->where('fb.start_time', $data->start_time)
                    ->where('fb.end_time', $data->end_time)
                    ->whereIn('fb.status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                    ->where('fb.date', $data->date)
                    ->where('fb.facility_id', $fac->id)
                    ->groupBy('fb.date')
                    ->select(
                        DB::raw('SUM(fb.attendance) as bookings'),
                        'o.id as order_id',
                    )
                    ->get();
            } else {
                $orders = DB::table('orders as o')
                    ->join('facility_bookings as fb', 'fb.order_id', '=', 'o.id')
                    ->where('o.customer_id', $customer->id)
                    ->whereIn('fb.status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                    ->where('o.venue_id', $data->venueId)
                    ->where('fb.date', $data->date)
                    ->where('fb.facility_id', $fac->id)
                    ->groupBy('fb.date')
                    ->select(
                        DB::raw('SUM(fb.attendance) as bookings'),
                        'o.id as order_id',
                    )
                    ->get();
            }
            if ($orders->count() > 0 && ($orders->sum('bookings') > $threshold->customer_threshold)) {
                return false;
            }
        }
        return true;
    }

    public function saveDisclaimers($customer, $venueServiceId, $venueId, $facilityBooking, $fac): void
    {
        if (isset($customer->disclaimers) && is_array($customer->disclaimers) && count($customer->disclaimers) > 0) {
            $service = VenueService::join('services as s', 's.id', 'venue_services.service_id')
                ->where('venue_services.id', $venueServiceId)
                ->select('s.name')
                ->first();
            $document = CustomerDocument::where('customer_id', $customer->id)
                ->whereIn('id_proof_type_id', [32,33])
                ->orderBy('id_proof_type_id')
                ->first();
            foreach ($customer->disclaimers as $disclaimer) {
                $pdfSvc = new PdfGeneratorService();
                $facilityName = isset($fac) ? $fac->name : '';
                $bookingDate = $facilityBooking->date;
                $data = (object)[
                    'facility_name' => $facilityName,
                    'customer_name' => $customer->first_name . ' ' . $customer->last_name,
                    'booking_date' => $bookingDate,
                    'service_name' => $service?->name,
                    'dob' => $customer->dob,
                    'id_proof_number' => $document?->id_proof_number,
                    'booking_time' => $facilityBooking->start_time,
                ];

                $path = $pdfSvc->generateDisclaimerPdf($disclaimer, $data, $venueId, $disclaimer->value);
                $facilityBookingDocuments = new FacilityBookingDocuments();
                $facilityBookingDocuments->facility_booking_id = $facilityBooking->id;
                $facilityBookingDocuments->document_name = $disclaimer->name;
                $facilityBookingDocuments->original_file_name = $disclaimer->name;
                $facilityBookingDocuments->document_path = $path;
                $facilityBookingDocuments->status_id = 1;
                $facilityBookingDocuments->save();

                $customerDocument = new CustomerDocument();
                $customerDocument->venue_id = $venueId;
                $customerDocument->customer_id = $customer->id;;
                $customerDocument->id_proof_type_id = 44;
                $customerDocument->id_proof_path = $path;
                $customerDocument->document_name = $facilityName . ' ' . $bookingDate;;
                $customerDocument->save();
            }
        }
    }

}
