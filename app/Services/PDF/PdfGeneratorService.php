<?php

namespace App\Services\PDF;

use App\Models\Venue;
use App\Service\Azure\AzureClientService;
use Barryvdh\DomPDF\Facade as PDF;
use Carbon\Carbon;

class PdfGeneratorService
{
    public function generateDisclaimerPdf($field, $data, $venueId, $image): string
    {
        $venue = Venue::with('b2c')->find($venueId);
        if (isset($data->customer_name)) {
            $field->content = str_replace('CUSTOMER_NAME', $data->customer_name, $field->content);
            $field->ar_content = str_replace('CUSTOMER_NAME', $data->customer_name, $field->ar_content);
        }
        if (isset($data->dob)) {
            $field->content = str_replace('DOB', $data->dob, $field->content);
            $field->ar_content = str_replace('DOB', $data->dob, $field->ar_content);
        }
        if (isset($data->id_proof_number)) {
            $field->content = str_replace('ID_NUMBER', $data->id_proof_number, $field->content);
            $field->ar_content = str_replace('ID_NUMBER', $data->id_proof_number, $field->ar_content);
        }
        if (isset($data->booking_time)) {
            $field->content = str_replace('BOOKING_TIME', $data->booking_time, $field->content);
            $field->ar_content = str_replace('BOOKING_TIME', $data->booking_time, $field->ar_content);
        }
        if (isset($data->booking_date)) {
            $field->content = str_replace('BOOKING_DATE', Carbon::createFromFormat('Y-m-d', $data->booking_date)->format('d F, Y'), $field->content);
            $field->ar_content = str_replace('BOOKING_DATE', Carbon::createFromFormat('Y-m-d', $data->booking_date)->format('d F, Y'), $field->ar_content);
        }
        if (isset($data->facility_name)) {
            $field->content = str_replace('FACILITY_NAME', $data->facility_name, $field->content);
            $field->ar_content = str_replace('FACILITY_NAME', $data->facility_name, $field->ar_content);
        }
        if (isset($data->service_name)) {
            $field->content = str_replace('SERVICE_NAME', $data->service_name, $field->content);
            $field->ar_content = str_replace('SERVICE_NAME', $data->service_name, $field->ar_content);
        }

        $ticketPdf = PDF::loadView('export.customer.disclaimer', [
            'venueDetails' => $venue,
            'image' => $image,
            'field' => $field
        ])->setPaper([0, 0, 612, 792], 'portrait');
        $file_name = 'disclaimer_' . $field->name . '_' . time() . '.pdf' ;
        $azureClientSvc = new AzureClientService(config('filesystems.disks.azure.container'));
        $date = Carbon::now()->format('Y/m/d');

        $path = "venue/$venueId/$date/customer-portal/customer/profile/" . $file_name;
        $azureClientSvc->store($ticketPdf->output(), $path);
        return $path;
    }
}


