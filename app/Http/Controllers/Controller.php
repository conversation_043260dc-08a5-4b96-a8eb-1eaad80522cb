<?php

namespace App\Http\Controllers;

use App\Models\AgeGroup;
use App\Models\Category;
use App\Models\Country;
use App\Models\Customer;
use App\Models\CustomerAdditionalData;
use App\Models\CustomerContact;
use App\Models\CustomerDocument;
use App\Models\CustomerLogin;
use App\Models\Document;
use App\Models\EventBookingTicket;
use App\Models\FacilityBookingTicket;
use App\Models\FieldConfiguration;
use App\Models\GeneralType;
use App\Models\Invoice;
use App\Models\Logging;
use App\Models\Order;
use App\Models\Product;
use App\Helpers\MailHelper;
use Illuminate\Support\Facades\Http;
use App\Models\ProductAvailability;
use App\Models\Religion;
use Illuminate\Http\UploadedFile;
use App\Models\Role;
use App\Models\TaxType;
use App\Models\UserVenue;
use App\Models\Venue;
use App\Models\VenueCustomer;
use App\Models\VenueCustomersTypes;
use App\Service\Azure\AzureClientService;
use Carbon\Carbon;
use Exception;
use Illuminate\Database\QueryException;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Bus\DispatchesJobs;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Storage;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use PhpOffice\PhpSpreadsheet\Style\Border;
use PhpOffice\PhpSpreadsheet\Worksheet\Drawing;
use PhpOffice\PhpSpreadsheet\Worksheet\MemoryDrawing;
use Symfony\Component\HttpFoundation\Response;
use MicrosoftAzure\Storage\Blob\BlobRestProxy;
use MicrosoftAzure\Storage\Blob\Models\CreateBlockBlobOptions;
use MicrosoftAzure\Storage\Blob\Models\GetBlobOptions;

class Controller extends BaseController
{
    use AuthorizesRequests, DispatchesJobs, ValidatesRequests;

    public $venueId = null;
    public $userId = null;
    public $user = null;
    public $venue = null;
    public $venues = [];

    public function __construct()
    {
        try {
            $user = auth('api')->user();
            $this->venueId = $user ? $user->current_venue_id : NULL;
            $this->venue = $user ? $user->venue : NULL;
            $this->venues = $user ? $user->venues : NULL;
            $this->venueIds = $user ? ($user->venues ? collect($user->venues)->pluck('venue_id')->toArray() : []) : NULL;
            $this->userId = $user ? $user->id : NULL;
            $this->user = $user;
        } catch (Exception $e) { // this exception handler is required as an addition to middleware as this is being called at every controller
            if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenInvalidException) {
                return response()->json(['status' => false, 'message' => 'Token is Invalid', 'data' => null], Response::HTTP_UNAUTHORIZED);
            } else if ($e instanceof \Tymon\JWTAuth\Exceptions\TokenExpiredException) {
                return response()->json(['status' => false, 'message' => 'Authorization token expired', 'data' => $this->getError($e)], Response::HTTP_UNAUTHORIZED);
            } else {
                return response()->json(['status' => false, 'message' => 'Authorization token not found', 'data' => $this->getError($e)], Response::HTTP_UNAUTHORIZED);
            }
        }
    }

    /**
     * Get error details
     *
     * @param Object $e
     * @return String
     */
    public function getError($e)
    {
        if (env('APP_ENV') == 'dev' || env('APP_ENV') == 'local') {
            $message = $e->getMessage();
            $message .= '  ||---- Developer debug data:' . $e->getFile() . ' ' . $e->getLine() . '----||';
        } else {
            if ($e instanceof QueryException) {
                $message = "Something went wrong.";
            } else {
                $message = $e->getMessage();
            }
        }
        $trace = $e->getMessage() . '  ||---- Developer debug data:' . $e->getFile() . ' ' . $e->getLine() . ', Trace as string: ' . $e->getTraceAsString() . '----||';
        Log::info("Error Message >> " . $trace);
        return $message;
    }

    /**
     * Generate unique sequence number
     *
     * @param string $table
     * @param string $prefix
     * @param integer $length
     * @return string
     */
    public function generateSequence($prefix, $id = null, $venueId = null)
    {
        if (isset($this->venue) && $this->venue && $this->venue->enable_order_seq) {
            if ($prefix == 'O') {
                $countOfOrders = Order::where('venue_id', $this->venueId)->where([['invoice_seq_no', '!=', null]])->count();
            } else if ($prefix == 'I') {
                $countOfOrders = Invoice::where('venue_id', $this->venueId)->where([['invoice_seq_no', '!=', null]])->count();
            } else if ($prefix == 'R') {
                $countOfOrders = Order::where('venue_id', $this->venueId)->whereIn('status_id', [4, 8, 22, 14, 21])->where([['invoice_seq_no', '!=', null]])->count();
            } else if ($prefix == 'C') {
                $countOfOrders = Order::where(['venue_id' => $this->venueId, 'status_id' => 13])->count();
            } else if ($prefix == 'RF') {
                $countOfOrders = Order::where(['venue_id' => $this->venueId, 'status_id' => 8])/*->whereNull('invoice_seq_no')*/->count();
            } else if ($prefix == 'T') {
                $countOfOrders = EventBookingTicket::count();
            } else if ($prefix == 'FT') {
                $countOfOrders = FacilityBookingTicket::count();
            }


            if ($countOfOrders) {
                if ($id) {
                    $countOfOrders = $id . $countOfOrders;
                }
                return ($prefix . '-' . sprintf("%013d", ($countOfOrders + 1)));
            } else {
                return ($prefix . '-' . sprintf("%013d", 1));
            }
        } else if ($venueId) {
            if ($prefix == 'I') {
                $countOfOrders = Order::where('venue_id', $venueId)->where([['invoice_seq_no', '!=', null]])->count();
            } else if ($prefix == 'R') {
                $countOfOrders = Order::where('venue_id', $venueId)->whereIn('status_id', [4, 8, 22, 14, 21])->where([['invoice_seq_no', '!=', null]])->count();
            } else if ($prefix == 'C') {
                $countOfOrders = Order::where(['venue_id' => $venueId, 'status_id' => 13])->count();
            } else if ($prefix == 'RF') {
                $countOfOrders = Order::where(['venue_id' => $venueId, 'status_id' => 8])->whereNull('invoice_seq_no')->count();
            } else if ($prefix == 'T') {
                $countOfOrders = EventBookingTicket::count();
            } else if ($prefix == 'FT') {
                $countOfOrders = FacilityBookingTicket::count();
            }
            if ($countOfOrders) {
                if ($id) {
                    $countOfOrders = $id . $countOfOrders;
                }
                return ($prefix . '-' . sprintf("%013d", ($countOfOrders + 1)));
            } else {
                return ($prefix . '-' . sprintf("%013d", 1));
            }
        }

        return strtoupper(uniqid($prefix . '-'));
    }


    /**
     * Validate for errors
     *
     * @param [Object] $request
     * @param [Array] $rules
     * @return Boolean
     */
    public function validationError($request, $rules, $message = null)
    {
        $validator = Validator::make($request->all(), $rules);
        if ($message != null) {
            $validator = Validator::make($request->all(), $rules, $message);
        }
        if ($validator->fails()) {
            return response()->json(['status' => false, 'message' => $validator->errors()->first(), 'data' => $validator->errors()], Response::HTTP_UNPROCESSABLE_ENTITY);
        }
        return false;
    }

    /**
     * Common pagination utility
     *
     * @param object $request
     * @param object $query
     * @param string $sortBy
     * @return Response
     */
    public function paginateOld($request, $query, $sortBy)
    {
        $page = 1;
        if ($request->has('page')) {
            $page = $request->input('page');
        }
        $perPage = 10;
        if ($request->has('per_page')) {
            if ($request->input('per_page') != null) {
                $perPage = $request->input('per_page');
            }
        }

        $offSet = ($page - 1) * $perPage;
        $total = $query->get()->count();
        $totalPages = ceil($total / $perPage);

        $orderBy = 'desc';
        if ($request->has('sort_order')) {
            $orderBy = $request->input('sort_order');
        }

        if ($request->has('order_by_custom')) {
            $sortBy = $request->input('order_by_custom');
        }

        $data = $query->orderBy($sortBy, $orderBy)->offset($offSet)->limit($perPage);

        $orderBy2 = 'desc';
        if ($request->has('sort_order2')) {
            $orderBy2 = $request->input('sort_order2');
        }
        $sortBy2 = 'desc';
        if ($request->has('sort_by')) {
            $sortBy2 = $request->input('sort_by');
            $data = $query->orderBy($sortBy2, $orderBy2);
        }

        $data = $data->get();

        return response()->json(['status' => true, 'total' => $total, 'total_pages' => $totalPages, 'current_page' => $page, 'data' => $data, 'message' => 'Success'], Response::HTTP_OK);
    }

    /**
     * Create or update customer
     *
     * @param object $request
     * @return object
     */
    public function createOrUpdateCustomer($request)
    {
        $mobile = $request->input('mobile');
        $email = $request->has('email') ? $request->input('email') : null;
        if ($request->has('name')) {
            $name = trim($request->input('name'));
            $first_name = substr($name, 0, strpos($name, ' '));
            $last_name = trim(substr($name, strlen($first_name)));
            if (!$first_name) {
                $first_name = $last_name;
                $last_name = NULL;
            }
        } elseif ($request->has('first_name') && $request->has('last_name')) {
            $first_name = $request->input('first_name');
            $last_name = trim($request->input('last_name'));
        } elseif ($request->has('first_name')) {
            $first_name = $request->input('first_name');
            $last_name = NULL;
        }

        if ($request->has('customer_id') && $request->customer_id > 0) {
            $customer = Customer::where('id', $request->input('customer_id'))->first();
            throw_if(!$customer, 'customer not found');
            $customerContact = CustomerContact::find($customer->customer_contacts_id);
        } else {
            $customer = Customer::join('customer_contacts as cc', 'cc.id', '=', 'customers.customer_contacts_id')->where(['cc.mobile' => $mobile])->select('customers.*', 'cc.email', 'cc.mobile');
            $customer = $customer->where(['first_name' => $first_name]);
            if ($email) {
                $customer = $customer->where(['cc.email' => $email]);
            }
            if ($last_name != NULL) {
                $customer = $customer->where(['last_name' => $last_name]);
            }
            $customer = $customer->first();
            if (!$customer) {
                $customer = new Customer();
                $customerContact = CustomerContact::where(['mobile' => $mobile, 'email' => $email])->first();
                if (!$customerContact) {
                    $customerContact = new CustomerContact;
                }
            } else {
                $customerContact = CustomerContact::find($customer->customer_contacts_id);
            }
        }

        $oldContact = clone $customerContact;
        $customerContact->mobile = $mobile;
        if ($request->has('name')) {
            $name = trim($request->input('name'));
            $first_name = substr($name, 0, strpos($name, ' '));
            $last_name = trim(substr($name, strlen($first_name)));
            if (!$first_name) {
                $first_name = $last_name;
                $last_name = '';
            }
            $customer->first_name = $first_name;
            $customer->last_name = trim($last_name);
        } else if ($request->has('first_name') && $request->input('last_name')) {
            $customer->first_name = trim($request->input('first_name'));
            $customer->last_name = trim($request->input('last_name'));
        } else if ($request->has('first_name')) {
            $customer->first_name = trim($request->input('first_name'));
        }
        if ($request->has('email')) {
            $customerContact->email = $email;
        }
        if ($request->has('dob')) {
            $customer->dob = Carbon::parse($request->input('dob'))->format('Y-m-d');
        }

        if ($customer->dob) {
            $customer->age_group = $this->getAgeGroup($customer->dob);
        } else {
            if ($request->has('age_group')) {
                if (!$customer->dob || ($customer->age_group !=  $request->age_group)) {
                    $customer->dob = $this->getDOB($request->age_group);
                }
                $customer->age_group = $request->age_group;
            }
        }

        if ($request->has('gender')) {
            $customer->gender = $request->input('gender');
        }
        if ($request->has('country_id')) {
            $customer->country_id = $request->input('country_id');
        }
        if ($request->has('notes')) {
            $customer->notes = $request->input('notes');
        }
        if ($request->has('nationality')) {
            $country = Country::where('name', $request->input('nationality'))->where('status_id', 1)->first();
            if ($country) {
                $customer->country_id = $country->id;
            }
        }
        $image = NULL;
        if ($request->hasFile('profile_image')) {
            $image = $request->file('profile_image');
        } else if ($request->hasFile('image')) {
            $image = $request->file('image');
        }

        if ($image != NULL) {
            $currentPath = $customer->profile_image != null ? $customer->profile_image : null;
            $file = $this->fileUpload($image, 'customer/profile', null, $currentPath, $request->input('venue_id'));
            $customer->profile_image = $file->file_path;
        }
        if ($request->has('opt_marketing')) {

            // if ($request->boolean('opt_marketing')) {
            //     $customer->opt_marketing = 1;
            // } else {
            //     $customer->opt_marketing = 2;
            // }
            if ($customer->id) {
                VenueCustomer::where(['customer_id' => $customer->id, 'venue_id' => $this->venueId])->update(['opt_marketing' => filter_var($request->input('opt_marketing'), FILTER_VALIDATE_BOOLEAN)]);
            }
        }
        if ($request->has('is_anonymous_visitor')) {
            $customer->is_anonymous_visitor = $request->input('is_anonymous_visitor');
        }
        if ($request->has('religion_id')) {
            $customer->religion_id = $request->input('religion_id');
            if (!$customer->religion){
                $religion = Religion::find($customer->religion_id);
                if ($religion) {
                    $customer->religion = $religion->name;
                }
            }
        }




        try {
            $customerContact->save();
        } catch (QueryException $dbException) {
            info('duplicate customer try');
            info(json_encode(['email' => $email, 'mobile' => $mobile]));
            $customerContact = CustomerContact::where(['mobile' => $mobile, 'email' => $email])->first();
            if (!$customerContact) {
                $customerContact = new CustomerContact;
                $customerContact->mobile = $mobile;
                if ($request->has('email')) {
                    $customerContact->email = $email;
                }
                $customerContact->save();
            }
        } finally {
            $customer->customer_contacts_id = $customerContact->id;
        }
        //        try {
        //            $customer->save();
        //
        //            $customerLogin = CustomerLogin::where(['customer_id' => $customer->id, 'venue_id' => $this->venueId])
        //                ->where(function ($q) use ($oldContact) {
        //                    $q->where('username', $oldContact->mobile)->orWhere('username', $oldContact->email);
        //                })
        //                ->first();
        //            if ($customerLogin) {
        //                $customerLogin->username = $mobile;
        //                $customerLogin->save();
        //            }
        //        } catch (QueryException $dbException) {
        //            info(json_encode(['email' => $email, 'mobile' => $mobile]));
        //            throw new Exception('Duplicate Customer is being created');
        //        }
        $customer->save();
        if ($request->has('additional_fields') && is_array($request->additional_fields)) {
            \Log::info("TESTING: Processing additional_fields", [
                'fields_count' => count($request->additional_fields),
                'customer_name' => $request->input('name', 'unknown'),
                'customer_id' => $request->input('customer_id', 'unknown')
            ]);

            foreach ($request->additional_fields as $key => $additional_field) {
                \Log::info("TESTING: Processing field", [
                    'customer_name' => $request->input('name', 'unknown'),
                    'key' => $key,
                    'field_keys' => array_keys($additional_field),
                    'has_value' => isset($additional_field['value']),
                    'has_id' => isset($additional_field['id']),
                    'field_type' => isset($additional_field['type']) ? $additional_field['type'] : 'not_provided'
                ]);

                if(!isset($additional_field['value'])){
                    \Log::info("TESTING: No value found, skipping");
                    continue;
                }

                // TEMPORARY: Handle disclaimer_form type fields upload to public/uploads for testing
                if (isset($additional_field['id'])) {
                    $field = FieldConfiguration::where('id', $additional_field['id'])
                        ->where('venue_id', $this->venueId)
                        ->first();

                    \Log::info("TESTING: Processing additional field", [
                        'field_id' => $additional_field['id'],
                        'field_found' => $field ? true : false,
                        'field_type' => $field ? $field->type : 'not_found',
                        'field_slug' => $field ? $field->slug : 'not_found',
                        'value_type' => gettype($additional_field['value']),
                        'is_uploaded_file' => $additional_field['value'] instanceof UploadedFile
                    ]);

                    if ($field && $field->type === 'disclaimer_form') {
                        \Log::info("TESTING: Found disclaimer_form field, checking file type", [
                            'field_slug' => $field->slug,
                            'value_type' => gettype($additional_field['value']),
                            'is_uploaded_file' => $additional_field['value'] instanceof UploadedFile
                        ]);

                        if ($additional_field['value'] instanceof UploadedFile) {
                            \Log::info("TESTING: Processing disclaimer_form field upload to public/uploads");

                            // Create uploads directory if it doesn't exist
                            $uploadsPath = public_path('uploads');
                            if (!file_exists($uploadsPath)) {
                                mkdir($uploadsPath, 0755, true);
                            }

                            // Generate unique filename
                            $file = $additional_field['value'];
                            $filename = time() . '_disclaimer_' . $field->slug . '_' . $file->getClientOriginalName();

                            // Move file to public/uploads
                            $file->move($uploadsPath, $filename);

                            // Log for testing
                            \Log::info("TESTING: Disclaimer form file uploaded to public/uploads/{$filename}");

                            // Skip the rest of processing to avoid Azure upload
                            continue;
                        } else {
                            \Log::info("TESTING: Disclaimer form value is not an UploadedFile", [
                                'value_type' => gettype($additional_field['value']),
                                'value_class' => is_object($additional_field['value']) ? get_class($additional_field['value']) : 'not_object',
                                'value_content' => is_string($additional_field['value']) ? substr($additional_field['value'], 0, 100) : 'not_string'
                            ]);
                        }
                    }
                }

                $field = FieldConfiguration::where('id', $additional_field['id'])
                    ->where('venue_id', $this->venueId)
                    ->first();
                throw_if(!$field, 'Invalid additional field ' . $additional_field['slug']);
                $customer_field = CustomerAdditionalData::where([
                    'field_id' => $field->id,
                    'customer_id' => $customer->id,
                    'venue_id' => $this->venueId
                ])->first();
                if (!$customer_field) {
                    $customer_field = new CustomerAdditionalData();
                    $customer_field->field_id = $field->id;
                    $customer_field->customer_id = $customer->id;
                    $customer_field->venue_id = $this->venueId;
                }
                throw_if(!($customer_field instanceof CustomerAdditionalData), 'Invalid additional field');
                if (isset($additional_field['value'])) {
                    if($additional_field['value'] instanceof UploadedFile){
                        $currentPath = $customer_field->value != null ? $customer_field->value : null;
                        $file = $this->fileUpload($additional_field['value'], 'customer/profile', null, $currentPath, $this->venueId);
                        $customer_field->value = $file->file_path;
                    }else{
                        $customer_field->value = is_array($additional_field['value'])
                            ? json_encode($additional_field['value'])
                            : $additional_field['value'];
                    }
                    $customer_field->save();
                }
            }
        }
        return $customer;
    }

    public function getDOB($ageGroup_id)
    {
        $dob = null;
        $ageGroup = AgeGroup::find($ageGroup_id);
        if ($ageGroup) {
            $min = $ageGroup->min;
            $max = $ageGroup->max ?? $ageGroup->min + 2;
            $middle = ($min + $max) / 2;
            // Calculate the birth year, months, and days based on the middle age
            $birthYear = now()->subYears(ceil($middle));
            $fractionalPart = $middle - floor($middle);
            $birthMonths = floor($fractionalPart * 12);
            $birthDays = round(($fractionalPart * 12 - $birthMonths) * 30);

            // Use Carbon to create a date with the calculated birth year, months, and days
            $dob = $birthYear->addMonths($birthMonths)->addDays($birthDays)->toDateString();
        }
        return $dob;
    }

    public function getAgeGroup($dob)
    {
        $ageGroupId = AgeGroup::where('venue_id', $this->venueId)
        ->where(function ($query) use ($dob) {
            $query->where(function ($subquery) use ($dob) {
                $subquery->where('min', '<=', Carbon::parse($dob)->diffInYears())
                    ->where('max', '>=', Carbon::parse($dob)->diffInYears());
            })
                ->orWhere(function ($subquery) {
                    $subquery->whereNull('min')
                        ->whereNull('max');
                })
                ->orWhere(function ($subquery) use ($dob) {
                    $subquery->whereNull('max')
                        ->where('min', '<=', Carbon::parse($dob)->diffInYears());
                });
        })
            ->value('id');
        if (!$ageGroupId) {
            $ageGroupId = AgeGroup::where(function ($query) use ($dob) {
                    $query->where(function ($subquery) use ($dob) {
                        $subquery->where('min', '<=', Carbon::parse($dob)->diffInYears())
                            ->where('max', '>=', Carbon::parse($dob)->diffInYears());
                    })
                        ->orWhere(function ($subquery) {
                            $subquery->whereNull('min')
                                ->whereNull('max');
                        })
                        ->orWhere(function ($subquery) use ($dob) {
                            $subquery->whereNull('max')
                                ->where('min', '<=', Carbon::parse($dob)->diffInYears());
                        });
                })
                ->whereNull('venue_id')
                ->value('id');
        }
        return $ageGroupId;
    }

    /**
     * File upload utility
     *
     * @param $file
     * @param string $folderName
     * @param integer $documentTypeId
     * @param string $currentPath
     * @return object
     */
    public function fileUploadBackup($file, $folderName, $documentTypeId, $currentPath = null, $venueId = null)
    {
        $s3 = Storage::disk('s3');
        if ($currentPath == null) {
            $document = new Document;
        } else {
            $document = Document::where('file_path', $currentPath)->first();
            if ($document) {
                $s3->delete($document->file_path);
            } else {
                $document = new Document;
            }
        }
        if ($documentTypeId == null) {
            $documentTypeId = GeneralType::where('slug', 'document-images')->pluck('id')->first();
        }
        $document->document_type_id = $documentTypeId;
        $originalFilename = $file->getClientOriginalName();
        $fileName = time() . '_' . uniqid() . '_' . preg_replace(array('/\s/', '/\.[\.]+/', '/[^\w_\.\-]/'), array('_', '.', ''), $originalFilename);
        if (strlen($fileName) > 250) {
            $fileName = substr($fileName, 0, 250);
        }

        $datePath = Carbon::now()->format('Y/m/d');
        $filePath = 'venue/' . ($venueId ? $venueId : $this->venueId) . "/$datePath/$folderName/" . $fileName;
        $s3->put($filePath, file_get_contents($file), 'public');
        $document->venue_id = $venueId ? $venueId : $this->venueId;
        $document->file_path = $filePath;
        $document->original_file_name = $originalFilename;
        $document->save();
        return $document;
    }

    protected function copyFiles3($sourceFilePath, $targetFilePath)
    {
        // Use the S3 disk
        $disk = Storage::disk('s3');

        // Copy the file
        //        $exists = $disk->has($sourceFilePath);
        //        info('$exists');
        //        info($exists);
        $disk->copy($sourceFilePath, $targetFilePath);

        return $targetFilePath;
    }

    public function copyFile($original_path, $target_path)
    {
        $azureClient = new AzureClientService();
        return $azureClient->copyFile($original_path, $target_path);
    }

    /**
     * Create venue customer for a customer record
     *
     * @param object $customer
     * @return void
     */
    public function createVenueCustomer($customer, $customerType = 'Walk-in', $venueId = null, $isAnonymousVisitor = false)
    {
        $customerVenue = $this->venueId;
        if ($venueId != null) {
            $customerVenue = $venueId;
        }
        $venueCustomer = VenueCustomer::where(['venue_id' => $customerVenue, 'customer_id' => $customer->id])->first();
        if (!$venueCustomer) {
            $venueCustomer = new VenueCustomer;
        }
        $customerTypeId = GeneralType::select('id')->where(['slug' => 'customer-type', 'name' => $customerType])->pluck('id')->first();
        $venueCustomer->venue_id = $customerVenue;
        $venueCustomer->customer_id = $customer->id;
        $venueCustomer->status_id = 1;

        if ($customer)

            // $venueCustomer->opt_marketing = $customer->opt_marketing ?? boolval(0);
            $venueCustomer->save();


        $venueCustomersTypes = VenueCustomersTypes::updateOrCreate([
            'customer_type_id' => $customerTypeId,
            'venue_customer_id' => $venueCustomer->id,
        ]);
        $venueCustomersTypes->save();
        $venue = Venue::find($customerVenue);
        if ($venue->allow_child_sync && !$isAnonymousVisitor) {
            $this->syncCustomers($venueCustomer, $customerVenue, $customer, $customerType);
        }
    }

    private function syncCustomers($venueCustomer, $venueId, $customer, $customerType)
    {
        $venues = Venue::where('id', $venueId)
            ->orWhere('parent_venue_id', $venueId)
            ->orWhere('id', function ($query) use ($venueId) {
                $query->select('parent_venue_id')
                    ->from('venues')
                    ->where('id', $venueId);
            })
            ->orWhere('parent_venue_id', function ($query) use ($venueId) {
                $query->select('parent_venue_id')
                    ->from('venues')
                    ->where('id', $venueId);
            })
            ->get();

        foreach ($venues as $venue) {
            if ($venueCustomer->venue_id != $venue->id) {
                $venueCustomer = VenueCustomer::where(['venue_id' => $venue->id, 'customer_id' => $customer->id])->first();
                if (!$venueCustomer) {
                    $venueCustomer = new VenueCustomer;
                }
                $venueCustomer->venue_id = $venue->id;
                $venueCustomer->customer_id = $customer->id;
                $venueCustomer->status_id = 1;

                $venueCustomer->save();

                $customerTypeId = GeneralType::select('id')->where(['slug' => 'customer-type', 'name' => $customerType])->pluck('id')->first();
                $venueCustomersTypes = VenueCustomersTypes::updateOrCreate([
                    'customer_type_id' => $customerTypeId,
                    'venue_customer_id' => $venueCustomer->id,
                ]);
                $venueCustomersTypes->save();
            }
        }
    }

    /**
     * save customer document for a customer record
     *
     * @param object $customer
     * @return void
     */
    public function createCustomerDocument($request, $customer_id, $venueId = null)
    {
        $customerVenueId = $this->venueId;
        if ($venueId != null) {
            $customerVenueId = $venueId;
        }
        if ($request->has('id_proof_type_id') && $request->id_proof_type_id > 0) {
            $customerDocument = CustomerDocument::where(['venue_id' => $customerVenueId, 'customer_id' => $customer_id, 'id_proof_type_id' => $request->id_proof_type_id])->first();
            if (!empty($customerDocument)) {
                $customerDocument->id_proof_number = $request->id_proof_number ?? null;
                if ($request->hasFile('id_proof')) {
                    $file = $this->fileUpload($request->file('id_proof'), 'facility/booking/id', null);
                    $customerDocument->id_proof_path = $file->file_path;
                }
            } else {
                $customerDocument = new CustomerDocument();
                $customerDocument->id_proof_type_id = $request->id_proof_type_id;
                $customerDocument->id_proof_number = $request->id_proof_number ?? null;
                $customerDocument->customer_id = $customer_id;
                if ($request->hasFile('id_proof')) {
                    $file = $this->fileUpload($request->file('id_proof'), 'facility/booking/id', null);
                    $customerDocument->id_proof_path = $file->file_path;
                }
            }
            $customerDocument->venue_id = $customerVenueId;
            $customerDocument->save();
        }
    }

    /**
     * save venue customer tag for a customer record
     *
     * @param object $customer
     * @return void
     */
    public function createVenueCustomerTag($request, $customer_id, $venueId = null)
    {
        $customerVenueId = $this->venueId;
        if ($venueId != null) {
            $customerVenueId = $venueId;
        }
        $venueCustomer = VenueCustomer::where(['venue_id' => $customerVenueId, 'customer_id' => $customer_id])->first();
        if ($venueCustomer) {
            if ($request->has('customer_tag') && is_array($request->customer_tag) && count($request->customer_tag) > 0) {
                $tagIds = array_column($request->customer_tag, 'id');
                if (count($tagIds) > 0) {
                    $venueCustomer->venueCustomerTags()->sync($tagIds);
                }
            } else {
                $venueCustomer->venueCustomerTags()->sync([]);
            }
        }
    }

    /**
     * Add venue logo
     *
     * @param object $spreadsheet
     * @return void
     */
    public function addLogoToExcel($spreadsheet)
    {
        if (isset($this->venue)) {
            $venueLogo = $this->venue->profile_image != null ? config('filesystems.disks.azure.account_url') . '/' . $this->venue->profile_image : null;
            $imageType = "png";
            if ($venueLogo != null) { //Dynamic Logo
                try {
                    $drawing = new MemoryDrawing();
                    if (strpos($venueLogo, ".png") === false && strpos($venueLogo, ".PNG") === false) {
                        $imageType = "jpg";
                        $gdImage = imagecreatefromjpeg($venueLogo);
                    } else {
                        $gdImage = imagecreatefrompng($venueLogo);
                        imageAlphaBlending($gdImage, true);
                        imageSaveAlpha($gdImage, true);
                    }
                    $drawing->setImageResource($gdImage);
                    if ($imageType == 'jpg') {
                        $drawing->setRenderingFunction(MemoryDrawing::RENDERING_JPEG);
                    } else {
                        $drawing->setRenderingFunction(MemoryDrawing::RENDERING_PNG);
                    }
                    $drawing->setMimeType(MemoryDrawing::MIMETYPE_DEFAULT);
                } catch (Exception $e) {
                    throw new \Exception('Venue logo image corrupted. Please update your logo.');
                }
            } else { // Krews Logo if no logo in DB
                $drawing = new Drawing();
                $drawing->setPath('../resources/views/export/logo.png');
            }
        } else {
            $drawing = new Drawing();
            $drawing->setPath('../resources/views/export/logo.png');
        }

        $drawing->setName('Company Logo');
        $drawing->setDescription('Company Logo image');
        $drawing->setResizeProportional(false);
        $drawing->setWidth(210);
        $drawing->setHeight(70);
        $drawing->setOffsetX(8);
        $drawing->setOffsetY(8);
        $drawing->setCoordinates('A1');
        $drawing->setWorksheet($spreadsheet->getActiveSheet());
    }

    /**
     * Add venue logo
     *
     * @param object $spreadsheet
     * @return void
     */
    public function addQPortalLogoToExcel($spreadsheet)
    {
        $drawing = new Drawing();
        $drawing->setPath(base_path() . '/resources/views/export/QPortal-Logo.png');
        $drawing->setName('Company Logo');
        $drawing->setDescription('Company Logo image');
        $drawing->setResizeProportional(false);
        $drawing->setWidth(210);
        $drawing->setHeight(70);
        $drawing->setOffsetX(8);
        $drawing->setOffsetY(8);
        $drawing->setCoordinates('A1');
        $drawing->setWorksheet($spreadsheet->getActiveSheet());
    }
    /**
     * Border style for excel
     *
     * @return array
     */
    public function getExcelBorderStyle()
    {
        return [
            'borders' => [
                'allBorders' => [
                    'borderStyle' => Border::BORDER_NONE,
                    'color' => ['hex' => '#000000'],
                ],
                'outline' => [
                    'borderStyle' => Border::BORDER_THIN,
                    'color' => ['hex' => '#000000'],
                ],
            ],
        ];
    }

    /**
     * Get cashier Id
     *
     * @param Object $data
     * @return Object
     */
    public function getCashier($venueId, $type)
    {
        try {
            $userVenue = UserVenue::where(['user_venues.venue_id' => $venueId, 'user_venues.status_id' => 1])
                ->join('users as u', 'u.id', 'user_venues.user_id')
                ->join('user_types as ut', 'ut.id', 'u.user_type_id')
                ->where('ut.name', $type)->select('u.*')->first();
            if ($userVenue) {
                return $userVenue->id;
            } else {
                return false;
            }
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Save Event Tickets function
     *
     * @param Object $data
     * @param Integer $productTypeID
     * @param Integer $id
     * @return Object
     */
    protected function saveProduct($data, $productTypeID, $id = null, $image = null,$venueId = null)
    {
        $update = false;
        if ($id == null) {
            $product = new Product;
            $product->venue_id = $venueId ?? $this->venueId;
            $product->tax_type_id = TaxType::select('id')->where('name', 'TAX FREE')->pluck('id')->first();
        } else {
            $update = true;
            $product = Product::where(['id' => $id])->whereIn('venue_id', $this->venueIds)->first();
            $product->name = $data->name;
        }
        $product->product_type_id = $productTypeID;
        if ($image != null && is_file($image)) {
            $currentPath = $product->image != null ? $product->image : null;
            $file = $this->fileUpload($image, "$productTypeID/product", null, $currentPath);
            $product->image = $file->file_path;
        } elseif (isset($data->image_path) && $data->image_path != null) {
            $product->image = $data->image_path;
        } elseif (isset($data->image) && $data->image != null) {
            $product->image = $data->image;
        }
//        elseif (isset($data['image_path']) && $data['image_path'] != null) {
//            $product->image = $data['image_path'];
//        }
        if (isset($data->description)) {
            $product->description = $data->description;
        } else {
            $product->description = null;
        }

        if (isset($data->ar_description)) {
            $product->ar_description = $data->ar_description;
        } else {
            $product->ar_description = null;
        }

        $product->name = $data->name;
        if (isset($data->ar_name)) {
            $product->ar_name = $data->ar_name;
        } else {
            $product->ar_name = null;
        }
        if (isset($data->outlet_id)) {
            $product->outlet_id = $data->outlet_id;
        } else {
            $product->outlet_id = null;
        }

        if (isset($data->allow_cashback) && $data->allow_cashback) {
            $product->allow_cashback = $data->allow_cashback;
            if (isset($data->cashback_percentage) && $data->cashback_percentage > 0 && $data->cashback_percentage != 'undefined') {
                if ($data->cashback_percentage > 100) {
                    throw new Exception('Percentage cannot be greater than 100 for product ' . $product->name);
                }
                $product->cashback_percentage = $data->cashback_percentage;
            } else {
                $product->cashback_percentage = 0.00;
            }
        } else {
            $product->allow_cashback = 0;
            $product->cashback_percentage = 0.00;
        }

        if (isset($data->price) && $product->price != $data->price || isset($data->price) && $product->tax_type_id != $data->tax_type_id) {
            $price = isset($data->price) ? $data->price : null;
            $product->price = $price;
            $tax = 0;
            if (isset($data->tax_type_id) && $price != null) {
                $taxPercentage = TaxType::select('percentage')->where('id', $data->tax_type_id)->pluck('percentage')->first();
                $tax = round($price * ($taxPercentage / 100),4,PHP_ROUND_HALF_EVEN);
                $product->tax_type_id = $data->tax_type_id;
                $product->tax_amount = $tax;
            } else {
                $product->tax_amount = $tax;
            }
            $product->total_price = $tax + $price;
        }

        if (isset($data->is_membership_only)) {
            $product->is_membership_only = $data->is_membership_only;
        }
        if (isset($data->enable_dependency)) {
            $product->enable_dependency = $data->enable_dependency;
        }
        if (isset($data->is_open_dated)) {
            $product->is_open_dated = $data->is_open_dated;
            if ($data->is_open_dated) {
                if (isset($data->expiry_days)) {
                    $product->expiry_days = $data->expiry_days;
                } else {
                    $product->expiry_days = null;
                }
            } else {
                $product->expiry_days = null;
            }
        }

        if (isset($data->enable_kiosk_sales)) {
            $product->enable_kiosk_sales = $data->enable_kiosk_sales;
        }
        if (isset($data->enable_b2b_sales)) {
            $product->enable_b2b_sales = $data->enable_b2b_sales;
        }
        if (isset($data->enable_b2e_sales)) {
            $product->enable_b2e_sales = $data->enable_b2e_sales;
        }
        if (isset($data->enable_b2g_sales)) {
            $product->enable_b2g_sales = $data->enable_b2g_sales;
        }
        if (isset($data->show_end_time)) {
            $product->show_end_time = $data->show_end_time;
        }
        if (isset($data->inventory_enable)) {
            $product->inventory_enable = $data->inventory_enable;
        }

        if (isset($data->enable_online_booking)) {
            $product->enable_online_booking = $data->enable_online_booking;
        }

        if (isset($data->benefit_excluded)) {
            $product->benefit_excluded = $data->benefit_excluded;
        }

        if (isset($data->ticket_type)) {
            $product->ticket_type = $data->ticket_type;
            $product->participant_count = $data->ticket_type == 'I' ? 1 : ($data->ticket_type == 'C' ? 2 : ($data->participant_count ?? 0));
        }
        if (isset($data->project_no) && $data->project_no) {
            $product->project_no = $data->project_no;
        }
        if (isset($data->task_name) && $data->task_name) {
            $product->task_name = $data->task_name;
        }
        if (isset($data->gl_code) && $data->gl_code) {
            $product->gl_code = $data->gl_code;
        }
        if (isset($data->transaction_type) && $data->transaction_type) {
            $product->transaction_type = $data->transaction_type;
        }
        if (isset($data->enable_scanner_product)) {
            $product->enable_scanner_product = $data->enable_scanner_product;
        }
        if (isset($data->parent_id) && $data->parent_id) {
            $product->parent_id = $data->parent_id;
        }
        if (isset($data->attribute_name) && $data->attribute_name) {
            $product->attribute_name = $data->attribute_name;
        }
        if (isset($data->enable_retail_inventory)) {
            $product->enable_retail_inventory = $data->enable_retail_inventory;
        }
        if (isset($data->is_pod)) {
            $product->is_pod = $data->is_pod;
        }
        if (isset($data->enable_admission)) {
            $product->enable_admission = $data->enable_admission;
        }
        if (isset($data->enable_applicable_venues)) {
            $product->enable_applicable_venues = $data->enable_applicable_venues;
        }
        if(isset($data->allow_visit)){
            $product->allow_visit = $data->allow_visit;
        }
        if (isset($data->admission_type)) {
            $product->admission_type = $data->admission_type;
            if($data->admission_type == 1) {
                $product->allow_visit = 1;
            }
        }

        if (isset($data->sku) && !empty($data->sku)) {
            $product->sku = $data->sku;
        }

        $product->save();
        if ($update) {
            Logging::create([
                'venue_id' => $this->venueId,
                'user_id' => $this->userId,
                'target_id' => $product->id,
                'model' => get_class($product),
                'table' => $product->getTable(),
                'action' => 'Update',
                'description' =>  "{$this->user->first_name} {$this->user->last_name} updated '{$product->name}' product.",
            ]);
        } else {
            Logging::create([
                'venue_id' => $this->venueId,
                'user_id' => $this->userId,
                'target_id' => $product->id,
                'model' => get_class($product),
                'table' => $product->getTable(),
                'action' => 'Create',
                'description' =>  "{$this->user->first_name} {$this->user->last_name} created '{$product->name}' product.",
            ]);
        }

        if (isset($data->availabilities)) {
            $singleAvailability = isset($data->single_availability) ? $data->single_availability : false;
            $this->saveProductAvailability($product->id, $data->availabilities, $singleAvailability);
        }

        //outlet assignments
        if (isset($data->outlets) && is_array($data->outlets)) {
            $this->syncProductOutlets($product, $data->outlets);
        }

        return $product;
    }

    protected function syncProductOutlets($product, $outletsData)
    {
        $syncData = [];

        // outletsData is an array of outlet IDs (simple format: [13, 14, 15])
        foreach ($outletsData as $outletId) {
            $syncData[$outletId] = [
                'status_id' => 1,
                'deleted_at' => null,
                'updated_at' => now()
            ];
        }

        // current outlet IDs for this product
        $currentOutletIds = $product->allOutlets()->pluck('venue_outlet_id')->toArray();

        // Soft delete outlets that are not in the new list
        $outletsToSoftDelete = array_diff($currentOutletIds, array_keys($syncData));
        if (!empty($outletsToSoftDelete)) {
            $product->allOutlets()->wherePivotIn('venue_outlet_id', $outletsToSoftDelete)
                    ->updateExistingPivot($outletsToSoftDelete, ['deleted_at' => now()]);
        }

        $product->outlets()->sync($syncData);
    }

    protected function saveProductAvailability($productId, $availabilityData, $singleAvailability = false)
    {
        if (!empty($availabilityData)) {
            foreach ($availabilityData as $availability) {
                $availability = (object)$availability;
                if ($singleAvailability) {
                    $productAvailability = ProductAvailability::firstOrCreate(['product_id' => $productId]);
                } else {
                    if (isset($availability->id)) {
                        $productAvailability = ProductAvailability::where(['id' => $availability->id])->first();
                    } else {
                        $productAvailability = new ProductAvailability;
                    }
                }
                $productAvailability->product_id = $productId;
                $productAvailability->start_time = $availability->start_time;
                $productAvailability->end_time = $availability->end_time;
                $productAvailability->weekdays = json_decode($availability->weekdays);
                $productAvailability->save();
            }
        }
    }

    /**
     * Get venue details
     *
     * @param object $request
     * @return object
     */
    protected function getVenue($request, $venueId)
    {
        $venue = Venue::whereId($venueId)->with([
            'integrations',
            'adminUser',
            'bankAccount',
            'subscriptions' => function ($query) {
                $query->select('venue_subscriptions.venue_id', 'venue_subscriptions.id as venue_subscription_id', 'venue_subscriptions.sub_module_id', 'sm.sub_module_name', 'sm.slug', 'sm.module_id', 'm.module_name')
                    ->join('sub_modules as sm', 'sm.id', '=', 'venue_subscriptions.sub_module_id')
                    ->join('modules as m', 'm.id', '=', 'sm.module_id')
                    ->orderBy('m.id', 'asc');
            },
            'services' => function ($query) {
                $query->select('venue_services.venue_id', 'venue_services.id as venue_service_id', 's.name', 's.image_path', 's.map_pin', 's.status_id', 's.service_type', 's.display_order', 's.id as service_id')
                    ->join('services as s', 's.id', '=', 'venue_services.service_id')
                    ->where(['s.status_id' => 1, 'venue_services.status_id' => 1]);
            },
            'documents' => function ($query) {
                $query->select('documents.venue_id', 'documents.id as document_id', 'documents.file_path', 'documents.description', 'documents.original_file_name', 't.name as document_type', 't.slug as document_slug')
                    ->join('general_types as t', 't.id', '=', 'documents.document_type_id')
                    ->whereIn('t.slug', ['venue_license', 'venue_agreement']);
            },
            'paymentGateway' => function ($q) {
                $q->select(
                    'venue_id',
                    'FAB_USERNAME as custom_payment_gateway_username',
                    'FAB_PASSWORD as custom_payment_gateway_password',
                    'FAB_MERCHANT as custom_payment_gateway_fab_merchant',
                    'FAB_STORE_ID as custom_payment_gateway_storeId',
                    'FAB_TERMINAL as custom_payment_gateway_terminal',
                    'FAB_URL as custom_payment_gateway_fab_url',
                    'NI_API_KEY as NI_API_KEY',
                    'NI_API_KEY as custom_payment_gateway_api_key',
                    'NI_OUTLET_REFERENCE as NI_OUTLET_REFERENCE',
                    'NI_OUTLET_REFERENCE as custom_payment_gateway_outlet_reference',
                    'TELR_STORE',
                    'TELR_AUTH_KEY',
                    'TELR_REMOTE_AUTH_KEY',
                    'TELR_API_KEY',
                    'GATEWAY_TYPE as custom_payment_gateway_type',
                    'STATUS_ID as custom_payment_gateway_status'
                );
            },
            'smtpDetails',
            'aiSensy',
            'AdConfig',
            'dctErpConfiguration'
        ])->first();
        if ($venue) {
            $venue->roles = $this->roles($request);
        }
        return $venue;
    }

    /**
     * Get roles for logged-in venue
     *
     * @return Object
     */
    protected function roles($status_id = 1, $hasPermissions = false)
    {
        $venueId = $this->venueId;
        $roles = Role::select(
            'roles.*',
            DB::raw('(select coalesce(count(distinct m.id),0)
            from role_permissions as rp
            join venue_subscriptions as vs on rp.venue_subscription_id = vs.id
            join sub_modules as s on vs.sub_module_id = s.id
            join modules as m on s.module_id = m.id where rp.role_id = roles.id ) as modules'),
            DB::raw('(select coalesce(count(distinct rp.id),0)
            from role_permissions as rp where rp.role_id = roles.id) as sub_modules')
        )
            ->where(function ($query) use ($venueId) {
                $query->where('venue_id', '=', $venueId)
                    ->orWhere('venue_id', null);
            })->where('status_id', $status_id);

        if ($hasPermissions) {
            $roles = $roles->whereHas('permissions')->get();
        } else {
            $roles = $roles->orderBy('roles.created_at', 'desc')->get();
        }
        return $roles;
    }

    /**
     * Save open product function
     *
     * @param Object $data
     * @return Object
     */
    public function saveOpenProduct($data, $venueId = null)
    {
        $categories = Category::where(['venue_service_id' => $data->venue_service_id, 'name' => 'open_product']);
        if ($categories->exists()) {
            $categories = $categories->first();
        } else {
            $categories = new Category();
            $categories->name = 'open_product';
            $categories->venue_service_id = $data->venue_service_id;
            $categories->status_id = 14;
            $categories->save();
        }
        $product = new Product;
        $product->venue_id = $venueId ?: $this->venueId;
        $product->name = $data->name;
        $product->price = ($data->price / $data->quantity);
        $product->tax_amount = isset($data->tax) ? ($data->tax / $data->quantity) : 0;
        $product->total_price = $product->price + $product->tax_amount;
        $product->tax_type_id = isset($data->tax) ? 1 : 2; // TODO: Add tax type
        $product->product_type_id = 6;
        $product->save();
        $product['category_id'] = $categories->id;
        return $product;
    }

    public function paginate($request, $query, $sortBy)
    {
        $page = 1;
        if ($request->has('page')) {
            $page = $request->input('page');
        }
        $perPage = 10;
        if ($request->has('per_page')) {
            if ($request->input('per_page') != null) {
                $perPage = $request->input('per_page');
            }
        }


        $orderBy = 'desc';
        if ($request->has('sort_order')) {
            $orderBy = $request->input('sort_order');
        }

        if ($request->has('order_by_custom')) {
            $sortBy = $request->input('order_by_custom');
        }

        $data = $query->orderBy($sortBy, $orderBy)->paginate($perPage);
        return response()->json(['status' => true, 'total' => $data->total(), 'total_pages' => $data->lastPage(), 'current_page' => $page, 'data' => $data->items(), 'message' => 'Success'], Response::HTTP_OK);
        // return response()->json(['status' => true, 'data' => $data, 'message' => 'Success'], Response::HTTP_OK);
    }

    public function getWalkInCustomer()
    {
        $customer = DB::table('customers', 'c')
            ->join('customer_contacts as cc', 'cc.id', '=', 'c.customer_contacts_id')
            ->where('cc.email', '=', '<EMAIL>')->where('cc.mobile', '=', '+************')
            ->select('c.*')
            ->first();
        if (!$customer) {
            throw new Exception('Customer not found');
        }
        $this->createVenueCustomer($customer);
        return $customer;
    }

    public function fileUpload($file, $folderName, $documentTypeId, $currentPath = null, $venueId = null)
    {

        $blobClient = BlobRestProxy::createBlobService(
            "DefaultEndpointsProtocol=https;AccountName=" . config('filesystems.disks.azure.account_name') . ";AccountKey=" . config('filesystems.disks.azure.account_key')
        );
        $blobOptions = new CreateBlockBlobOptions();
        $blobOptions->setContentType($file->getMimeType());

        $originalFilename = $file->getClientOriginalName();
        $fileName = time() . '_' . uniqid() . '_' . preg_replace(array('/\s/', '/\.[\.]+/', '/[^\w_\.\-]/'), array('_', '.', ''), $originalFilename);
        if (strlen($fileName) > 250) {
            $fileName = substr($fileName, 0, 250);
        }
        $datePath = now()->format('Y/m/d');
        $filePath = 'venue/' . ($venueId ? $venueId : $this->venueId) . "/$datePath/$folderName/" . $fileName;

        if ($currentPath == null) {
            $document = new Document;
        } else {
            $document = Document::where('file_path', $currentPath)->first();
            if ($document) {
                $options = new GetBlobOptions();
                $options->setSnapshot('');
                // Check if the blob exists
                try {
                    $blobExists = $blobClient->getBlob(
                        config('filesystems.disks.azure.container'),
                        $document->file_path,
                        $options
                    );
                    if ($blobExists) {
                        // Delete the existing blob in Azure Blob Storage
                        $blobClient->deleteBlob(config('filesystems.disks.azure.container'), $document->file_path);
                    }
                } catch (Exception $e) {
                }
            } else {
                $document = new Document;
            }
        }

        if ($documentTypeId == null) {
            $documentTypeId = GeneralType::where('slug', 'document-images')->pluck('id')->first();
        }

        $document->document_type_id = $documentTypeId;
        // Upload the file to Azure Blob Storage
        $blobClient->createBlockBlob(
            config('filesystems.disks.azure.container'),
            $filePath,
            file_get_contents($file),
            $blobOptions
        );

        $document->venue_id = $venueId ? $venueId : $this->venueId;
        $document->file_path = $filePath;
        $document->original_file_name = $originalFilename;
        $document->save();

        return $document;
    }

    public function checkPermissions($permission)
    {
        $user = Auth::user();
        if (!$user) {
            return response()->json(['status' => false, 'message' => 'You are not authorized to perform this operation', 'data' => null], Response::HTTP_FORBIDDEN);
        }
        $permissions = explode(':', $permission);
        $slug = $permissions[0];
        $access = $permissions[1];
        if ($access == 'allow_read') {
            $accessIndex = 0;
        } else if ($access == 'allow_write') {
            $accessIndex = 1;
        } else if ($access == 'allow_delete') {
            $accessIndex = 2;
        } else if ($access == 'allow_export') {
            $accessIndex = 3;
        } else {
            $accessIndex = 4;
        }
        $slugs = explode('|', $slug);

        $payload = auth()->payload()->toArray();
        $modules = (array) $payload['modules'];

        $keys = array_keys($modules);
        $subscribedModules = array_intersect($keys, $slugs);

        $subscribedModulesSlugs = array_values($subscribedModules);

        if ($this->checkModulesPermission($modules, $subscribedModulesSlugs, $accessIndex)) {
            return true;
        } else {
            return false;
        }
    }

    private function checkModulesPermission($modules, $subscribedModules, $accessIndex)
    {
        foreach ($subscribedModules as $val) {
            if ($modules[$val][$accessIndex] === 1) {
                return true;
            }
        }
        return false;
    }

    public function createOtp()
    {
        return mt_rand(100000, 999999);
    }

    public function createOrUpdateCustomerICP($request)
    {
        try {
            $mobile = $request->input('mobile') ? $request->input('mobile') : null;
            $email = $request->input('email') ? $request->input('email') : null;
            $customer = null;
            if ($request->has('customer_id') && $request->customer_id > 0) {
                $customer = Customer::where('id', $request->input('customer_id'))->first();
                if ($customer) {
                    return $customer;
                }
            }
            if (isset($request->id_proof_number) && isset($request->id_proof_type_id)) {
                if ($request->id_proof_type_id == 32) {
                    $customer = Customer::where(['idn' => $request->id_proof_number, 'status_id' => 1])->first();
                }
                if (!$customer && $request->id_proof_type_id == 33) {
                    $customerDoc = CustomerDocument::where(['id_proof_type_id' => $request->id_proof_type_id, 'id_proof_number' => $request->id_proof_number, 'venue_id' => $this->venueId])->first();
                    if ($customerDoc) {
                        $customer = Customer::where(['id' => $customerDoc->customer_id, 'status_id' => 1])->first();
                    }
                }
            }
            if ($customer) {
                return $customer;
            }
            $customerContact = CustomerContact::where('mobile', $mobile)->where('email', $email)->first();
            [$first_name, $last_name] = $this->getFirstLastName($request->name);
            if ($customerContact) {
                $customer = Customer::where(['customer_contacts_id' => $customerContact->id, 'first_name' => $first_name, 'last_name' => $last_name])->first();
            }
            if ($customer) {
                return $customer;
            }
            if (!$customer) {
                if (! $customerContact) {
                    $customerContact = new CustomerContact();
                    $customerContact->mobile = $mobile;
                    $customerContact->email = $email;
                    $customerContact->save();
                }
                $customer = new Customer();
                $customer->customer_contacts_id = $customerContact->id;
                $customer->first_name = $first_name;
                $customer->last_name = $last_name;
                if ($this->venue->save_pii_data) {
                    if (isset($request->id_proof_type_id) && isset($request->id_proof_number) && $request->id_proof_type_id == 32 && $request->id_proof_number != null && $request->id_proof_number != "null") {
                        $customer->idn = $request->id_proof_number;
                    }
                    if ($request->has('dob') && $request->dob != null && $request->dob != "null") {
                        $customer->dob = Carbon::parse($request->input('dob'))->format('Y-m-d');
                    }
                    if ($request->has('age_group')) {
                        $customer->age_group = $request->age_group;
                    }
                    if ($request->has('gender') && $request->gender != "null" && $request->gender != null) {
                        $customer->gender = ucfirst($request->input('gender'));
                    }
                    if ($request->has('country_id')) {
                        $customer->country_id = $request->input('country_id');
                    } else if ($request->has('nationality')) {
                        $country = Country::where('name', $request->input('nationality'))->where('status_id', 1)->first();
                        if ($country) {
                            $customer->country_id = $country->id;
                        }
                    }
                    if ($request->has('religion')) {
                        $customer->religion = $request->input('religion');
                    }
                    $image = NULL;
                    if ($request->hasFile('profile_image')) {
                        $image = $request->file('profile_image');
                    } else if ($request->hasFile('image')) {
                        $image = $request->file('image');
                    }
                    if ($image != NULL) {
                        $currentPath = $customer->profile_image != null ? $customer->profile_image : null;
                        $file = $this->fileUpload($image, 'customer/profile', null, $currentPath, $request->input('venue_id'));
                        $customer->profile_image = $file->file_path;
                    }
                }
                $customer->save();
            }
            return $customer;
        } catch (QueryException $dbException) {
            Log::error("DuplicateCustomer:" . $dbException->getMessage());
            throw new \Exception('Duplicate Customer is being created');
        }
    }
    public function getFirstLastName($fullName)
    {
        $first_name = null;
        $last_name = null;
        if (isset($fullName)) {
            $name = trim($fullName);
            $first_name = substr($name, 0, strpos($name, ' '));
            $last_name = trim(substr($name, strlen($first_name)));
            if (!$first_name) {
                $first_name = $last_name;
                $last_name = null;
            }
        }
        return [$first_name, $last_name];
    }
    public function getUuid()
    {
        $uuid = (string) Str::uuid();
        if (VenueCustomer::where('customer_uuid', $uuid)->exists()) {
            return $this->getUuid();
        }
        // Return the unique UUID
        return $uuid;
    }
    public function isBaps()
    {
        $b2c = $this->venue->b2c;
        return $b2c && isset($b2c->colors['mode']) && $b2c->colors['mode'] === 'baps';
    }

    function format_amount($amount, $decimalCount = 4)
    {
        if (strpos($amount, '.') === false) {
            return $amount + 0; // ensures numeric return
        }

        $formatted = rtrim(rtrim(number_format($amount, $decimalCount, '.', ''), '0'), '.');

        return $formatted + 0; // convert back to number
    }

    public function syncWithQApp($venue)
    {
        try {
             if ($venue->profile_image != null)
                $venue->profile_image = config('filesystems.disks.azure.account_url') . '/' . $venue->profile_image;
            if ($venue->heat_map_image != null)
                $venue->heat_map_image = config('filesystems.disks.azure.account_url') . '/' . $venue->heat_map_image;
            $Q_APIUrl = env("QAPI_URL");
            if ($Q_APIUrl != null) {
                Http::post(env("QAPI_URL") . '/api/v2/public/sync-venue', [
                    'venue' => $venue
                ]);
            }
        } catch (\Exception $e) {
        }
    }
}
