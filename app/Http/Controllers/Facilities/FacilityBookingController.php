<?php

namespace App\Http\Controllers\Facilities;

use App\Helper\DiscountHelper;
use App\Helper\FacilityBookingHelper;
use App\Helper\FacilityHelper;
use App\Helper\InvoiceHelper;
use App\Helper\MailHelper;
use App\Helper\OrderHelper;
use App\Http\Controllers\Orders\OrderController;
use App\Http\Controllers\VenuePartner\Baps\BapsDataController;
use App\Jobs\SendFacilityBookingApprovalMailJob;
use App\Models\B2cConfiguration;
use App\Models\Country;
use App\Models\CustomerAdditionalData;
use App\Models\FieldConfiguration;
use App\Models\Outlet;
use App\Helper\PermissionHelper;
use App\Http\Controllers\Controller;
use App\Http\Controllers\Invoices\InvoiceController;
use App\Jobs\SendFacilityBookingRescheduleMailJob;
use App\Jobs\SendOperatorFacilityBookingRescheduleMailJob;
use App\Mail\FacilityBookingApprovedMail;
use App\Mail\FacilityBookingEmail;
use App\Mail\FacilityBookingRescheduleMail;
use App\Mail\FacilityBookingThresholdEmail;
use App\Mail\TemplateEmails;
use App\Mail\templateMails;
use App\Models\b2b;
use App\Models\ProductDependency;
use App\Models\RaceParticipants;
use App\Models\Races;
use App\Models\VenueCustomer;
use App\Models\Category;
use App\Models\CompanySaleOrder;
use App\Models\CreditOrder;
use App\Models\Customer;
use App\Models\CustomerConfigurations;
use App\Models\Facility;
use App\Models\FacilityBooking;
use App\Models\FacilityBookingItem;
use App\Models\FacilityBookingRepeat;
use App\Models\FacilityBookingTicket;
use App\Models\FacilityRental;
use App\Models\FacilityThresholdEmailLog;
use App\Models\GroupCustomer;
use App\Models\Invoice;
use App\Models\InvoiceItem;
use App\Models\Logging;
use App\Models\Order;
use App\Models\OrderItem;
use App\Models\OrderPayment;
use App\Models\PaymentMethod;
use App\Models\Product;
use App\Models\ProductCategory;
use App\Models\ProductInventorySales;
use App\Models\ProductType;
use App\Models\RentalProduct;
use App\Models\VenueIntegrations;
use App\Models\VenueService;
use App\Models\VenueServiceConfiguration;
use App\Models\Weekday;
use App\Service\Payment\PaymentService;
use App\Services\DCD\DCDService;
use App\Services\OpenApi\Free\FacilityBookingService;
use Carbon\Carbon;
use Carbon\CarbonInterval;
use Carbon\CarbonPeriod;
use Google\Service\Analytics\Upload;
use Illuminate\Http\Request;
use Illuminate\Http\UploadedFile;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use App\Models\ScannerLog;
use PDF;
use App\Models\SalesTeamOrderCommission;
use App\Models\SalesTeamCommission;
use PHPUnit\Util\Exception;
use Symfony\Component\HttpFoundation\Response;


class FacilityBookingController extends Controller
{
    /**
     * Constructor
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Get facility schedules
     *
     * @return Response
     */
    public function getFacilitySchedules(Request $request)
    {
        $rules = [
            'venue_service_id' => 'required|integer',
            'date' => 'required|date_format:Y-m-d',
            'game_formation_id' => 'nullable|array',
        ];

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        try {
            $venueId = $request->has('venueId') && $request->venueId?$request->venueId:$this->venueId;
            $venueServiceId = $request->input('venue_service_id');
            $venueConfiguration = VenueServiceConfiguration::where(['venue_service_id' => $venueServiceId])->first();

            $facilities = FacilityBookingHelper::facilityBookingDetails($request, $venueId);
            //            Log::info("requests dataaaa" . json_encode($request->all()));
            //            Log::info("facility details" . json_encode($facilities));
            if ($request->has('game_formation_id')) {
                $facilities = $facilities->whereHas('facilityGameFormations', function ($query) use ($request) {
                    $query->whereIn("facility_game_formations.game_formation_id", $request->input('game_formation_id'));
                });
            }
            $date = $request->date;
            $facilities = $facilities->with(['isFacilityOnlineDisabled' => function ($query) use ($date) {
                $query->where('disable_date', $date);
            }]);
            $outlet = Outlet::join('outlet_facilities as of', 'of.outlet_id', 'outlets.id')
            ->where(['outlets.user_id' => $this->userId, 'of.status_id' => 1])
            ->select('outlets.*', 'of.facility_id')->get();
            if ($outlet->isNotEmpty()) {
                if ($outlet[0]->enable_booking == 1) {
                    $facilityIds = $outlet->pluck('facility_id')->toArray();  // Get the array of `facility_id`s
                    $facilities = $facilities->whereIn('facilities.id', $facilityIds);
                }
            }
            $facilities = $facilities->get();
            $venueConfiguration->filter_date = $date;
            $result = (object)['facilities' => $facilities, 'configuration' => $venueConfiguration];

            return response()->json(['status' => true, 'message' => 'success', 'data' => $result], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }
    /**
     * Get facility schedules
     *
     * @return Response
     */
    public function getFacilityBasedSchedules(Request $request)
    {
        $rules = [
            'venue_service_id' => 'required|integer',
            'facility_id' => 'required|integer',
            'weekdays' => 'required|string',
            'date' => 'required|date_format:Y-m-d',
            'game_formation_id' => 'nullable|array',
        ];

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        $weekdays = $request->input('weekdays');
        $dates = explode(',', $weekdays);

        try {
            $venueServiceId = $request->input('venue_service_id');
            $venueConfiguration = VenueServiceConfiguration::where(['venue_service_id' => $venueServiceId])->first();
            $facility = Facility::find($request->input('facility_id'));
            if (!$facility) {
                throw new \Exception('Facility not found');
            }
            $facilities = null;
            if ($facility->per_capacity == 1) {
                foreach ($dates as $key => $date) {
                    $temp = FacilityBookingHelper::singleFacilityBookingDetailsCapacityBased($request, $date, $this->venueId);

                    if ($request->has('game_formation_id')) {
                        $temp = $temp->whereHas('facilityGameFormations', function ($query) use ($request) {
                            $query->whereIn("facility_game_formations.game_formation_id", $request->input('game_formation_id'));
                        });
                    }
                    $temp = $temp->with(['isFacilityOnlineDisabled' => function ($query) use ($date) {
                        $query->where('disable_date', $date);
                    }]);
                    $facilities[$date] = $temp->first();
                    $venueConfiguration->filter_date = $request->input('date');
                }
            } else {
                foreach ($dates as $key => $date) {
                    $temp = FacilityBookingHelper::singleFacilityBookingDetailsTimeBased($request, $date, $this->venueId);

                    if ($request->has('game_formation_id')) {
                        $temp = $temp->whereHas('facilityGameFormations', function ($query) use ($request) {
                            $query->whereIn("facility_game_formations.game_formation_id", $request->input('game_formation_id'));
                        });
                    }
                    $temp = $temp->with(['isFacilityOnlineDisabled' => function ($query) use ($date) {
                        $query->where('disable_date', $date);
                    }]);
                    $facilities[$date] = $temp->first();
                    $venueConfiguration->filter_date = $request->input('date');
                }
            }
            //            //            Log::info("requests dataaaa" . json_encode($request->all()));
            //            //            Log::info("facility details" . json_encode($facilities));
            //            if ($request->has('game_formation_id')) {
            //                $facilities = $facilities->whereHas('facilityGameFormations', function ($query) use ($request) {
            //                    $query->whereIn("facility_game_formations.game_formation_id", $request->input('game_formation_id'));
            //                });
            //            }
            //            $date = $request->date;
            //            $facilities = $facilities->with(['isFacilityOnlineDisabled' => function ($query) use ($date) {
            //                $query->where('disable_date', $date);
            //            }]);
            //            $facilities = $facilities->get();
            //            $venueConfiguration->filter_date = $date;
            $result = (object)['facilities' => $facilities, 'configuration' => $venueConfiguration];

            return response()->json(['status' => true, 'message' => 'success', 'data' => $result], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }


    /**
     * Get facility for booking
     *
     * @return Response
     */
    public function getFacilityDataForBooking(Request $request)
    {
        $rules = [
            'facility_id' => 'required|integer',
            'date' => 'required|date_format:Y-m-d',
            'start_time' => 'required|date_format:H:i:s',
            'increment' => 'required|numeric',
            'min_booking_time' => 'required|numeric',
            'booking_id' => 'required|numeric',
            'per_capacity' => 'numeric',
        ];

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        try {
            $venueId = $request->has('venueId') && $request->venueId?$request->venueId:$this->venueId;
            $isB2b = false;
            $b2bId = false;
            if ($request->has('is_b2b') && $request->is_b2b == 1) {
                $isB2b = true;
            }
            if ($request->has('b2b_id') && $request->b2b_id) {
                $b2bId = $request->b2b_id;
            }
            $date = $request->input('date');
            $dayOfWeek = Carbon::parse($request->input('date'))->englishDayOfWeek;
            $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();
            $facilityId = $request->input('facility_id');
            $bookingId = $request->input('booking_id');

            $minBookingTime = (int)$request->input('min_booking_time');
            $startTime = $request->input('start_time');
            $endTime = Carbon::parse($startTime)->addMinutes($minBookingTime)->format('H:i:s');
            if ($endTime == '00:00:00') {
                $endTime = '23:59:59';
            }
            $rentalPackage = FacilityRental::where(['facility_id' => $facilityId, 'status_id' => 1])
                ->when(
                    DB::table('facility_rentals')->where('facility_id', $facilityId)->where('facility_rentals.status_id', 1)->whereRaw("(facility_rentals.weekdays & $bitValue)> 0")
                        ->where('is_seasonal', 1)->where('start_date', '<=', $date)->where('end_date', '>=', $date)->exists(),
                    function ($query) use ($date, $facilityId, $bitValue) {
                        return $query->where('is_seasonal', 1)->where('facility_rentals.start_date', '<=', $date)->whereRaw("(facility_rentals.weekdays & $bitValue)> 0")->where('facility_rentals.end_date', '>=', $date)->where('facility_id', $facilityId)->where('facility_rentals.status_id', 1);
                    },
                    function ($query) {
                        return $query->where('is_seasonal', 0);
                    }
                )
                ->orderBy('end_time', 'desc')->first();
            //            $rentalPackage = FacilityRental::where(['facility_id' => $facilityId, 'status_id' => 1])->whereRaw("'$endTime' between facility_rentals.start_time and facility_rentals.end_time")->orderBy('end_time', 'desc');
            //            if (!$rentalPackage->exists()) {
            //                return response()->json(['status' => true, 'message' => 'Booking error! End time is beyond facility working hours', 'data' => null], Response::HTTP_CONFLICT);
            //            } else {
            //                $rentalPackage = $rentalPackage->first();
            //                Log::info($rentalPackage);
            //            }
            //            $bookingEndTime = Carbon::parse(($date . " " . $startTime))->addMinutes($minBookingTime);
            //            $lastEndTime = Carbon::parse(($date . " " . $rentalPackage->end_time));
            //            if ($endTime != '23:59:59' && $bookingEndTime->greaterThan($lastEndTime)) {
            //                return response()->json(['status' => true, 'message' => 'Booking error! End time is beyond facility working hours', 'data' => null], Response::HTTP_CONFLICT);
            //            }
            $bookingEndTime = Carbon::parse(($date . " " . $startTime))->addMinutes($minBookingTime);
            $lastEndTime = Carbon::parse(($date . " " . $rentalPackage->end_time));
            if (($endTime != '23:59:59' && $bookingEndTime->greaterThan($lastEndTime)) && (!$request->input('enable_overnight_booking'))) {
                return response()->json(['status' => true, 'message' => 'Booking error! End time is beyond facility working hours', 'data' => null], Response::HTTP_CONFLICT);
            }

            $endDate = FacilityBookingHelper::getFacilityBookingEndDate($startTime, $endTime, $date);
            if ($endDate) {
                $endTime2 = $endTime;
                $request1 = new Request(['date' => $date, 'facility_id' => $request->facility_id, 'same_day' => 1]);
                $facilityController = (new FacilityController());
                $sameDayTiming = $facilityController->getFacilityTimings($request1);
                if (!json_decode($sameDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get same day end time');
                } else {
                    $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                }

                $request2 = new Request(['date' => $endDate, 'facility_id' => $facilityId, 'same_day' => 1]);
                $nextDayTiming = $facilityController->getFacilityTimings($request2);
                if (!json_decode($nextDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get next day start time');
                } else {
                    $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                }
            }

            $perCapacity = 0;
            if ($request->has('per_capacity')) {
                $perCapacity = $request->input('per_capacity');
            }
            $request->request->add(['end_time' => $endTime]);
            $request->request->add(['with_rentals' => true]);

            if ($bookingId == 0) {
                $facilityAvailability = FacilityHelper::facilityAvailability($request, $request->input('venue_service_id'), $venueId);

                if ($facilityAvailability->count() == 0) {
                    return response()->json(['status' => true, 'message' => 'Booking slot not available for selected time', 'data' => $facilityAvailability], Response::HTTP_CONFLICT);
                } else if ($perCapacity == 0 && $facilityAvailability->count() > 0 && $facilityAvailability[0]->{'facilityRentals'}->count() > 1) {
                    $rentalProducts = FacilityBookingHelper::getOverlappingTimeProducts($facilityId, $startTime, $endTime, $bitValue,$date);

                    //                    if($endDate){
                    //                        $dayOfWeek = Carbon::parse($endDate)->englishDayOfWeek;
                    //                        $bitValue2 = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();
                    //                        $rentalProducts2 = FacilityBookingHelper::getOverlappingTimeProducts($facilityId, $startTime2, $endTime2, $bitValue2);
                    //                    }
                    if ($rentalProducts->count() == 0) {
                        return response()->json(['status' => true, 'message' => 'Booking slot not available for selected time', 'data' => $rentalProducts], Response::HTTP_CONFLICT);
                    }
                }
            } else {
                $facilityBooking = FacilityBooking::find($bookingId);
                if ($facilityBooking->facility_booking_duplicate_id != null) {
                    $duplicateId = $facilityBooking->facility_booking_duplicate_id;
                    $facilityBooking = FacilityBooking::where('id', function ($query) use ($duplicateId) {
                        $query->select('facility_booking_id')->from('facility_booking_duplicates')->where('id', '=', $duplicateId);
                    })->first();
                    $startTime = $facilityBooking->start_time;
                    $endTime = $facilityBooking->end_time;
                }
            }
            /* -------------------------------------------------------------------------- */
            // getFacilityWithProducts this function is used for picking rental products for that particular facility
            /* -------------------------------------------------------------------------- */
            $facility = $this->getFacilityWithProducts($facilityId, $startTime, $endTime, $bitValue, $date,$venueId);

            $facility->default_products = [];
            if ($bookingId == 0) {
                if ($perCapacity == 0 && $facilityAvailability[0]->facilityRentals->count() > 1) {
                    $facility->default_products = $rentalProducts;
                } else if ($perCapacity == 0) {
                    $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                    /* -------------------------------------------------------------------------- */
                    /*           To avoid 11:59 issue where duration will be 1 min less           */
                    /* -------------------------------------------------------------------------- */
                    if ($duration % 5 == 4) {
                        $duration += 1;
                    }
                    $facility->default_products = FacilityBookingHelper::getRentalProducts($facilityId, $duration, $startTime, $endTime, $bitValue, $date);
                    if (isset($facility->default_products[0]))
                        $facility->default_products = $facility->default_products[0]['products'];
                    else
                        $facility->default_products = [];
                    // $facility->facilityRentals->filter(function ($product) use ($minBookingTime) {
                    //     return $product->duration == $minBookingTime;
                    // })->values();
                }
            }


            if ($facility->facilityRentals->count() > 0) {
                $nextBookedSlot = FacilityBooking::where('start_time', '<=', $lastEndTime)->where('start_time', '>=', $startTime)
                    ->where(['facility_id' => $request->input('facility_id')])
                    ->whereNotIn('status_id', [2])
                    ->where('date', $request->input('date'))->orderBy('facility_bookings.start_time');
                if ($request->input('booking_id') > 0) {
                    $nextBookedSlot = $nextBookedSlot->where('id', '!=', $request->input('booking_id'));
                }
                if ($nextBookedSlot->exists()) {
                    $lastEndTime = Carbon::parse($date . " " . $nextBookedSlot->pluck('start_time')->first());
                }

                $increment = $request->input('increment');
                $endTimes = [];

                if (Carbon::parse($date . " " . $endTime)->lt($lastEndTime)) {
                    $intervals = CarbonInterval::minutes($increment)->toPeriod($date . " " . $endTime, $lastEndTime);

                    foreach ($intervals as $date) {
                        $endTimes[] = (object)['time' => $date->format('H:i:s'), 'formatted' => $date->format('h:i a')];
                    }
                    $endTimes[] = (object)['time' => $lastEndTime->format('H:i:s'), 'formatted' => $lastEndTime->format('h:i a')];
                } else {
                    $endTimes = [(object)['quantity' => 1, 'time' => $endTime, 'formatted' => Carbon::parse($date . " " . $endTime)->format('h:i a')]];
                }
                $facilityRentals = $facility->facilityRentals;
                $bookingsGroupedDaily = collect();
                $bookingsGroupedNonDaily = collect();

                if ($perCapacity == 1){
                    if (PermissionHelper::venueHasPermissionForSubModule('rpi',$this->venueId)){
                        $inventoryEnabledRentals = $facility->facilityRentals->filter(function ($rental) {
                            return $rental->inventory_enable == 1;
                        });

                        if ($inventoryEnabledRentals->isNotEmpty()) {
                            $dailyRentals = $inventoryEnabledRentals->filter(function ($rental) {
                                return $rental->inventory_period === 'D';
                            });

                            $nonDailyRentals = $inventoryEnabledRentals->filter(function ($rental) {
                                return $rental->inventory_period !== 'D';
                            });

                            $dailyProductIds = $dailyRentals->pluck('product_id')->unique()->toArray();
                            $nonDailyProductIds = $nonDailyRentals->pluck('product_id')->unique()->toArray();

                            if (!empty($dailyProductIds)) {
                                $bookingsGroupedDaily = $this->getDailyBookingsByProductIds($facilityId, $date, $dailyProductIds)->groupBy('product_id');
                            }

                            if (!empty($nonDailyProductIds)) {
                                $nonDailyBookings = $this->getNonDailyBookingsByProductIds($facilityId, $date, $startTime, $endTime, $nonDailyProductIds);
                                $bookingsGroupedNonDaily = $nonDailyBookings->groupBy('product_id');
                            }
                            $facilityRentals = $facility->facilityRentals->map(function ($facilityRental) use ($facility, $bookingsGroupedDaily, $bookingsGroupedNonDaily) {

                                if ($facilityRental->inventory_enable == 1) {
                                    if ($facilityRental->inventory_period === 'D') {
                                        $bookings = $bookingsGroupedDaily->get($facilityRental->product_id, collect());
                                    } else {
                                        $bookings = $bookingsGroupedNonDaily->get($facilityRental->product_id, collect());
                                    }

                                    $facilityRental->booked_capacity = $bookings->sum('quantity');
                                } else {
                                    $facilityRental->allowed_capacity = $facility->capacity;
                                    $facilityRental->booked_capacity = 0;
                                }
                                $facilityRental->remaining_capacity = $facilityRental->allowed_capacity - $facilityRental->booked_capacity;

                                return $facilityRental;
                            })->filter(function ($facilityRental) {
                                return $facilityRental->booked_capacity < $facilityRental->allowed_capacity;
                            })->values();
                        }

                        if ($facilityRentals->count() === 0){
                            return response()->json(['status' => false, 'message' => "Ticket limit reached — please increase capacity to continue.", 'data' => null], Response::HTTP_CONFLICT);
                        }
                    }
                    if (PermissionHelper::venueHasPermissionForSubModule('ftd',$this->venueId)){

                        $productWithDependency = $facilityRentals->filter(function ($rental) {
                            return $rental->enable_dependency == 1;
                        });

                        $dependencies = ProductDependency::whereIn('product_id', $productWithDependency->pluck('product_id'))
                            ->orWhereIn('dependent_id', $productWithDependency->pluck('product_id'))
                            ->get();

                        $dailyDependents = $dependencies->filter(function ($dependency) {
                            return $dependency->period === 'D';
                        });
                        $nonDailyDependents = $dependencies->filter(function ($dependency) {
                            return $dependency->period !== 'D';
                        });

                        $dailyDependentProductIds = $dailyDependents->pluck('product_id')
                            ->merge($dailyDependents->pluck('dependent_id'))
                            ->unique()
                            ->toArray();

                        $nonDailyDependentProductIds = $nonDailyDependents->pluck('product_id')
                            ->merge($nonDailyDependents->pluck('dependent_id'))
                            ->unique()
                            ->toArray();
                        $bookingsGroupedDaily = collect();
                        $bookingsGroupedNonDaily = collect();

                        if (!empty($dailyDependentProductIds)) {
                            $dailyBookings = $this->getDailyBookingsByProductIds($facilityId, $date, $dailyDependentProductIds);
                            $bookingsGroupedDaily = $dailyBookings->groupBy('product_id');
                        }

                        if (!empty($nonDailyDependentProductIds)) {
                            $nonDailyBookings = $this->getNonDailyBookingsByProductIds($facilityId, $date, $startTime, $endTime, $nonDailyDependentProductIds);
                            $bookingsGroupedNonDaily = $nonDailyBookings->groupBy('product_id');
                        }

                        $dependencyMap = [];

                        foreach ($dependencies as $dependency) {
                            $dependencyMap[$dependency->product_id][] = $dependency->dependent_id;
                            $dependencyMap[$dependency->dependent_id][] = $dependency->product_id;
                        }

                        $filteredFacilityRentals = $facilityRentals->filter(function ($rental) use ($facilityRentals, $dependencyMap, $bookingsGroupedDaily, $bookingsGroupedNonDaily) {

                            $productId = $rental->product_id;

                            // If this product itself is already booked, block it
//                    if (
//                        ($bookingsGroupedDaily && $bookingsGroupedDaily->has($productId)) ||
//                        ($bookingsGroupedNonDaily && $bookingsGroupedNonDaily->has($productId))
//                    ) {
//                        return false;
//                    }

                            // Check dependencies: if any dependent product is booked, block this rental
                            if (!empty($dependencyMap[$productId])) {
                                foreach ($dependencyMap[$productId] as $dependentProductId) {
                                    if (
                                        ($bookingsGroupedDaily && $bookingsGroupedDaily->has($dependentProductId)) ||
                                        ($bookingsGroupedNonDaily && $bookingsGroupedNonDaily->has($dependentProductId))
                                    ) {
                                        return false;
                                    }
                                }
                            }

                            return true; // No conflicts, keep it
                        })->values();
                        if ($filteredFacilityRentals->count() === 0){
                            $pIds = $dependencies->pluck('product_id')->unique()->filter(function ($item) use ($dependencies){
                                return !in_array($item,$dependencies->pluck('dependent_id')->unique()->toArray());
                            })->toArray();
                            $filteredFacilityRentals = $facilityRentals->filter(function ($rental) use ($pIds) {
                                return in_array($rental->product_id, $pIds);
                            })->values();
                        }
                        $facilityRentals = $filteredFacilityRentals;
                    }
                }


                $facility->setRelation('facilityRentals', $facilityRentals);
                $facility->end_times = $endTimes;
            } else {
                $endTime = Carbon::parse($endTime);
                $facility->end_times = [(object)['quantity' => 1, 'time' => $endTime->format('H:i:s'), 'formatted' => $endTime->format('h:i a')]];
            }
            $facility->start_time = $startTime;
            $facility->end_time = Carbon::parse($endTime)->format('H:i:s');

            $b2bProducts = [];
            if ($isB2b) {
                $b2bProducts = $this->getB2bProducts($b2bId);
            }
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $facility, 'b2b' => $b2bProducts], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    private function getDailyBookingsByProductIds($facilityId, $date, $dailyProductIds){
        return FacilityBooking::where('facility_bookings.facility_id', $facilityId)
            ->whereDate('facility_bookings.date', $date)
            ->whereNotIn('facility_bookings.status_id', [2, 13, 22])
            ->whereNotIn('oi.status_id', [2, 13, 22])
            ->whereIn('oi.product_id', $dailyProductIds)
            ->join('order_items as oi', 'oi.order_id', '=', 'facility_bookings.order_id')
            ->select('facility_bookings.id', 'facility_bookings.start_time', 'oi.product_id', 'oi.quantity')
            ->get();
    }
    private function getNonDailyBookingsByProductIds($facilityId,$date,$startTime,$endTime,$nonDailyProductIds){
        return FacilityBooking::where('facility_bookings.facility_id', $facilityId)
            ->whereDate('facility_bookings.date', $date)
            ->whereNotIn('facility_bookings.status_id', [2, 13, 22])
            ->whereNotIn('oi.status_id', [2, 13, 22])
            ->whereIn('oi.product_id', $nonDailyProductIds)
            ->where('facility_bookings.start_time', '<', $endTime)
            ->where('facility_bookings.end_time', '>', $startTime)
            ->join('order_items as oi', 'oi.order_id', '=', 'facility_bookings.order_id')
            ->select('facility_bookings.id', 'facility_bookings.start_time', 'oi.product_id', 'oi.quantity')
            ->get();
    }

    public function getB2bProducts($b2bId)
    {
        $b2b = b2b::with('type')->find($b2bId);
        $typename = strtolower($b2b->type->name);

        $ptype = ProductType::where('name', 'Facility')->first();
        if (!$ptype) {
            throw new \Exception('Product types not found');
        }
        $products = DB::table('products', 'p')
            ->leftJoin('tax_types as tt', 'tt.id', '=', 'p.tax_type_id')
            ->join('rental_products as rp', 'rp.product_id', '=', 'p.id')
            ->join('b2b_products_configuration as bp', 'bp.product_id', '=', 'p.id')
            ->leftJoin('tax_types as ttb', 'ttb.id', '=', 'bp.tax_type_id')
            ->where(['p.venue_id' => $this->venueId, 'rp.status_id' => 1, 'p.status_id' => 1, 'p.product_type_id' => $ptype->id, 'bp.status_id' => 1, "enable_{$typename}_sales" => 1])
            ->select(
                'bp.id as id',
                'p.id as product_id',
                'p.name as name',
                DB::raw('if(bp.status_id is null,2,bp.status_id) as status_id'),
                DB::raw('if(bp.price is null,p.price,bp.price) as price'),
                DB::raw('if(bp.tax_amount is null,p.tax_amount,bp.tax_amount) as tax_amount'),
                DB::raw('if(bp.total_price is null,p.total_price,bp.total_price) as total_price'),
                DB::raw('if(bp.tax_type_id is null,p.tax_type_id,bp.tax_type_id) as tax_type_id'),
                DB::raw('if(ttb.name is null,tt.name,ttb.name) as tax_type'),
            );

        if ($b2bId) {
            $products = $products->where('bp.b2b_id', $b2bId);
            $products = $products->get();
            if (!$products->count() > 0) {
                $products = DB::table('products', 'p')
                    ->leftJoin('tax_types as tt', 'tt.id', '=', 'p.tax_type_id')
                    ->join('rental_products as rp', 'rp.product_id', '=', 'p.id')
                    ->join('b2b_products_configuration as bp', 'bp.product_id', '=', 'p.id')
                    ->leftJoin('tax_types as ttb', 'ttb.id', '=', 'bp.tax_type_id')
                    ->where(['p.venue_id' => $this->venueId, 'rp.status_id' => 1, 'p.status_id' => 1, 'p.product_type_id' => $ptype->id, 'bp.status_id' => 1, "enable_{$typename}_sales" => 1])
                    ->select(
                        'bp.id as id',
                        'p.id as product_id',
                        'p.name as name',
                        DB::raw('if(bp.status_id is null,2,bp.status_id) as status_id'),
                        DB::raw('if(bp.price is null,p.price,bp.price) as price'),
                        DB::raw('if(bp.tax_amount is null,p.tax_amount,bp.tax_amount) as tax_amount'),
                        DB::raw('if(bp.total_price is null,p.total_price,bp.total_price) as total_price'),
                        DB::raw('if(bp.tax_type_id is null,p.tax_type_id,bp.tax_type_id) as tax_type_id'),
                        DB::raw('if(ttb.name is null,tt.name,ttb.name) as tax_type'),
                    )->whereNull('bp.b2b_id')->get();
            }
        } else {
            $products = $products->whereNull('bp.b2b_id');
            $products = $products->get();
        }
        return $products->count() > 0 ? $products->toArray() : [];
    }

    /**
     * Get rental package/products details for a facility
     *
     * @param integer $facilityId
     * @param string $startTime
     * @param string $endTime
     * @param integer $bitValue
     * @return object
     */
    private function getFacilityWithProducts($facilityId, $startTime, $endTime, $bitValue, $date = null, $venueId = null)
    {

        $venueId = $venueId?$venueId:$this->venueId;
//        $facilityClosingTime = FacilityRental::selectRaw('max(end_time) as closing_time')
//            ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")->where(['facility_id' => $facilityId, 'status_id' => 1])->first();

        $facilityClosingTime = FacilityRental::selectRaw('max(end_time) as closing_time')
            ->where('facility_id', $facilityId)
            ->where('status_id', 1)
            ->where(function($query) use ($bitValue, $date) {
                // If is_seasonal is 1, add date range condition
                $query->where(function($q) use ($bitValue, $date) {
                    $q->where('is_seasonal', 1)
                        ->whereRaw("(facility_rentals.weekdays & ?) > 0", [$bitValue])
                        ->whereDate('start_date', '<=', $date)
                        ->whereDate('end_date', '>=', $date);
                })
                    // If is_seasonal is 0, apply the regular condition
                    ->orWhere(function($q) use ($bitValue) {
                        $q->where('is_seasonal', 0)
                            ->whereRaw("(facility_rentals.weekdays & ?) > 0", [$bitValue]);
                    });
            })
            ->first();


//        $facilityOpeningTime = FacilityRental::selectRaw('min(start_time) as opening_time')
//            ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")->where(['facility_id' => $facilityId, 'status_id' => 1])->first();

        $facilityOpeningTime = FacilityRental::selectRaw('min(start_time) as opening_time')
            ->where('facility_id', $facilityId)
            ->where('status_id', 1)
            ->where(function($query) use ($bitValue, $date) {
                // If is_seasonal is 1, add date range condition
                $query->where(function($q) use ($bitValue, $date) {
                    $q->where('is_seasonal', 1)
                        ->whereRaw("(facility_rentals.weekdays & ?) > 0", [$bitValue])
                        ->whereDate('start_date', '<=', $date)
                        ->whereDate('end_date', '>=', $date);
                })
                    // If is_seasonal is 0, apply the regular condition
                    ->orWhere(function($q) use ($bitValue) {
                        $q->where('is_seasonal', 0)
                            ->whereRaw("(facility_rentals.weekdays & ?) > 0", [$bitValue]);
                    });
            })
            ->first();

        $facilityClosingTime = $facilityClosingTime->closing_time;
        $facilityOpeningTime = $facilityOpeningTime->opening_time;
        return Facility::with([
            'facilityRentals' => function ($query) use ($startTime, $endTime, $bitValue, $facilityClosingTime, $date, $facilityId) {
                //                $query->select(
                //                    'facility_rentals.start_time',
                //                    'facility_rentals.end_time',
                //                    'facility_rentals.id',
                //                    'facility_rentals.facility_id',
                //                    'facility_rentals.timing_template_id',
                //                    'p.name',
                //                    'rp.is_full_day as is_full_day',
                //                    'p.price',
                //                    'p.product_type_id',
                //                    'p.is_open_dated',
                //                    'p.expiry_days',
                //                    'p.id as product_id',
                //                    'p.tax_amount',
                //                    'p.ticket_type',
                //                    'p.is_membership_only as is_membership_only',
                //                    DB::raw('GROUP_CONCAT(pm.membership_id) as membership_id'),
                //                    'p.participant_count',
                //                    'pc.category_id',
                //                    'rp.duration',
                //                    DB::raw('1 as quantity')
                //                )->join('facility_rental_products as frp', 'frp.facility_rental_id', '=', 'facility_rentals.id')
                //                    ->join('rental_products as rp', 'rp.id', '=', 'frp.rental_product_id')
                //                    ->join('products as p', 'p.id', '=', 'rp.product_id')
                //                    ->leftJoin('product_memberships as pm', function ($join) {
                //                        $join->on('p.id', '=', 'pm.product_id')->where('pm.status_id', '=', '1');
                //                    })
                //                    ->join('product_categories as pc', 'pc.product_id', '=', 'p.id')
                //                    ->where(function ($query) use ($startTime, $endTime) {
                //                        $query->where(function ($query) use ($startTime, $endTime) {
                //                            $query->where('facility_rentals.start_time', '<=', $startTime);
                //                            $query->where('facility_rentals.end_time', '>=', $endTime);
                //                        });
                //                        $query->orWhere(function ($query) use ($startTime, $endTime) {
                //                            $query->where(function ($query) use ($startTime) {
                //                                $query->whereRaw("facility_rentals.start_time < '$startTime' and facility_rentals.end_time > '$startTime'");
                //                            })->orWhere(function ($query) use ($endTime) {
                //                                $query->whereRaw("facility_rentals.start_time < '$endTime' and facility_rentals.end_time > '$endTime'");
                //                            });
                //                        })->orderBy('duration', 'desc')->orderBy('start_time');
                //                    })->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")
                //                    ->where(function ($query) use ($bitValue) {
                //                        $query->where(function ($query) use ($bitValue) {
                //                            $query->whereRaw("(rp.weekdays_value & $bitValue)> 0")
                //                                ->where('rp.specific_day', 1);
                //                        })->orWhere('rp.specific_day', 0)
                //                            ->orWhereNull('rp.specific_day');
                //                    })
                //                    ->whereRaw("if(rp.duplicate_booking = 1, ADDTIME('$endTime', SEC_TO_TIME(rp.duplicate_booking_duration * 60)) <= '$facilityClosingTime', 1) = 1")
                //                    ->groupBy('p.id');

                $query->select([
                    'facility_rentals.start_time',
                    'facility_rentals.end_time',
                    'facility_rentals.id',
                    'facility_rentals.facility_id',
                    'facility_rentals.timing_template_id',
                    'p.name',
                    'rp.is_full_day as is_full_day',
                    //                    'p.price',
                    'p.product_type_id',
                    'p.is_open_dated',
                    'p.expiry_days',
                    'p.id as product_id',
                    DB::raw('CASE WHEN psp.tax IS NOT NULL THEN psp.tax ELSE p.tax_amount END AS tax_amount'),
                    DB::raw('CASE WHEN psp.price IS NOT NULL THEN psp.price ELSE p.price END AS price'),
                    DB::raw('CASE WHEN psp.price IS NOT NULL THEN psp.price ELSE p.price END AS product_price'),
                    //                    'p.tax_amount',
                    'p.ticket_type',
                    'psp.id as seasonal_pricing_id',
                    'p.is_membership_only as is_membership_only',
                    DB::raw('GROUP_CONCAT(pm.membership_id) as membership_id'),
                    'p.participant_count',
                    'pc.category_id',
                    'rp.duration',
                    DB::raw('1 as quantity'),
                    'p.inventory_enable',
                    'pi.quantity as allowed_capacity',
                    'pi.inventory_period',
                    'p.enable_dependency',
                ])
                    ->join('facility_rental_products as frp', 'frp.facility_rental_id', '=', 'facility_rentals.id')
                    ->join('rental_products as rp', 'rp.id', '=', 'frp.rental_product_id')
                    ->join('products as p', 'p.id', '=', 'rp.product_id')
                    ->leftJoin('product_inventories as pi','pi.product_id','p.id')
                    ->when(request()->get('kiosk'), function ($query) {
                        return $query->where('p.enable_kiosk_sales', 1);
                    })
                    ->leftJoin('product_seasonal_pricing as psp', function ($join) use ($date) {
                        $join->on('psp.product_id', '=', 'p.id')->when($date, function ($query) use ($date) {
                            $query->where('psp.start_date', '<=', $date)->where('psp.end_date', '>=', $date);
                        })->where('psp.status_id', 1);
                    })
                    ->leftJoin('product_memberships as pm', function ($join) {
                        $join->on('p.id', '=', 'pm.product_id')->where('pm.status_id', '=', '1');
                    })
                    ->join('product_categories as pc', 'pc.product_id', '=', 'p.id')
                    ->where(function ($query) use ($startTime, $endTime) {
                        $query->where(function ($query) use ($startTime, $endTime) {
                            $query->where('facility_rentals.start_time', '<=', $startTime);
                            $query->where('facility_rentals.end_time', '>=', $endTime);
                        });
                        $query->orWhere(function ($query) use ($startTime, $endTime) {
                            $query->where(function ($query) use ($startTime) {
                                $query->whereRaw("facility_rentals.start_time < '$startTime' and facility_rentals.end_time > '$startTime'");
                            })->orWhere(function ($query) use ($endTime) {
                                $query->whereRaw("facility_rentals.start_time < '$endTime' and facility_rentals.end_time > '$endTime'");
                            });
                        })->orderBy('duration', 'desc')->orderBy('start_time');
                    })
                    ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")
                    ->where(function ($query) use ($bitValue) {
                        $query->where(function ($query) use ($bitValue) {
                            $query->whereRaw("(rp.weekdays_value & $bitValue)> 0")
                                ->where('rp.specific_day', 1);
                        })->orWhere('rp.specific_day', 0)
                            ->orWhereNull('rp.specific_day');
                    })
                    ->whereRaw("if(rp.duplicate_booking = 1, ADDTIME('$endTime', SEC_TO_TIME(rp.duplicate_booking_duration * 60)) <= '$facilityClosingTime', 1) = 1")
                    ->where(function ($query) use ($date, $facilityId, $bitValue) {
                        $query->when(
                            DB::table('facility_rentals')->whereRaw("(weekdays & $bitValue)> 0")->where('facility_id', $facilityId)->where('facility_rentals.status_id', 1)
                                ->where('is_seasonal', 1)->where('start_date', '<=', $date)->where('end_date', '>=', $date)->exists(),
                            function ($query) use ($date, $facilityId, $bitValue) {
                                return $query->where('is_seasonal', 1)->whereRaw("(facility_rentals.weekdays & $bitValue)> 0")->where('facility_rentals.start_date', '<=', $date)->where('facility_rentals.end_date', '>=', $date)->where('facility_id', $facilityId)->where('facility_rentals.status_id', 1);
                            },
                            function ($query) {
                                return $query->where('is_seasonal', 0);
                            }
                        );
                    })
                    ->groupBy('p.id');
            },
            'categories' => function ($query) use ($bitValue, $startTime, $endTime, $date) {
                $query->with(['products' => function ($query) use ($bitValue, $startTime, $endTime, $date) {

                    $query->leftJoin('product_inventories as pi', 'pi.product_id', '=', 'products.id')
                        ->leftJoin('product_inventory_rules as pir', 'pir.product_inventory_id', '=', 'pi.id')
                        ->leftJoin('product_seasonal_pricing as psp', function ($join) use ($date) {
                            $join->on('psp.product_id', '=', 'products.id')->when($date, function ($query) use ($date) {
                                $query->where('psp.start_date', '<=', $date)->where('psp.end_date', '>=', $date)->where('psp.status_id', 1);
                            }, function ($query) {
                                $query->where('psp.status_id', 12);
                            });
                        })
                        ->where('products.status_id', 1)
                        ->where(function ($query) use ($bitValue, $startTime, $endTime) {
                            $query->whereRaw("(pir.weekdays & $bitValue)> 0")
                                ->where(function ($query) use ($startTime, $endTime) {
                                    $query->where('pir.start_time', '<=', $startTime);
                                    $query->where('pir.end_time', '>=', $endTime);
                                });
                        })
                        ->orWhere(function ($query) {
                            $query->orWhere(function ($query) {
                                $query->where('products.inventory_enable', 0)->where('products.status_id', 1);
                            });
                            $query->orWhere(function ($query) {
                                $query->where('products.status_id', 1)->whereNull('pir.weekdays');
                            });
                        })
                        ->select(
                            'products.id as id',
                            'products.*',
                            //                            'products.total_price as total_amount',
                            DB::raw('CASE WHEN psp.total IS NOT NULL THEN psp.total ELSE products.total_price END AS total_amount'),
                            'pi.quantity as actual_qty',
                            'pi.inventory_period as inventory_period',
                            DB::raw("'1' as quantity"),
                            DB::raw("(select sum(pis.quantity) from product_inventory_sales as pis where pis.product_inventory_id = pi.id and pis.status_id = 1 and date = '$date') as sales"),
                            DB::raw("(select sum(pis.quantity) from product_inventory_sales as pis
                                                join order_items as oi on oi.id = pis.order_item_id
                                                join facility_bookings as fb on fb.order_id = oi.order_id
                                                    where pis.product_inventory_id = pi.id and pis.status_id = 1 and
                                                    (
                                                        (fb.start_time < '$startTime' and fb.start_time > '$endTime')
                                                        or (fb.end_time < '$startTime' and fb.end_time > '$endTime')
                                                        or (fb.start_time = '$startTime' and fb.end_time = '$endTime')
                                                    )
                                                    and  pis.date = '$date' and fb.status_id <> 2)
                                                as time_sale")
                        )
                        ->groupBy('products.id');
                }])->whereIn('categories.status_id', [1, 14]);
            }
        ])->withCount(['bookings as total_attendance' => function ($query) use ($date) {
            $query->select(DB::raw('SUM(facility_bookings.attendance)'))
                ->where('facility_bookings.date', '=', $date)->where('facility_bookings.status_id', '!=', 2);
        }])
//            ->select([
//                'facilities.*',
//                DB::raw("'$facilityClosingTime' as closing_time"),
//                DB::raw("'$facilityOpeningTime' as opening_time"),
//            ])
            ->addSelect([
                DB::raw("'$facilityClosingTime' as closing_time"),
                DB::raw("'$facilityOpeningTime' as opening_time"),
            ])
            ->where(['venue_id' => $venueId, 'status_id' => 1, 'id' => $facilityId])
            ->orderBy('name', 'asc')->first();
    }

    //    public function checkFacilityBooking($request){
    //        $count = FacilityBooking::
    //    }

    /**
     * Create new facility booking
     *
     * @param Request $request
     * @return Response
     */
    public function store(Request $request)
    {
        $facilityAvailability = FacilityHelper::facilityAvailability($request, $request->input('venue_service_id'), $this->venueId);
        if ($facilityAvailability->count() == 0) {
            return response()->json(['status' => true, 'message' => 'Time slot not available for selected date', 'data' => $facilityAvailability], Response::HTTP_CONFLICT);
        }
        //        $checkAvailability = $this->checkFacilityBooking($request);

        return $this->addOrEditBooking($request);
    }

    private function addOrEditBooking($request)
    {
        //info(json_encode($request->all()));

        try {
            $rules = [
                'date' => 'required|date_format:Y-m-d',
                'venue_service_id' => 'required|exists:venue_services,id',
                'facility_id' => 'required|exists:facilities,id',
                'start_time' => 'date_format:H:i:s',
                'end_time' => 'required|date_format:H:i:s|after:start_time',
                'products' => 'array|nullable',
                'products.*.name' => 'nullable|string',
                'products.*.price' => 'nullable|numeric',
                'products.*.product_id' => 'required|integer',
                'products.*.order_item_id' => 'nullable|integer',
                'products.*.quantity' => 'required|numeric',
            ];
            $isEnableOverNight = false;
            if ($request->has('enable_overnight_booking') && $request->input('enable_overnight_booking')) {
                if($request->enable_overnight_booking == 1) {
                    $rules['end_time'] = 'required|date_format:H:i:s';
                    $isEnableOverNight = true;
                }
            }
//            if ($request->has('enable_overnight_booking') && $request->enable_overnight_booking == 0) {
//                $rules['end_time'] = 'date_format:H:i:s|after:start_time';
//                $isEnableOverNight = true;
//            }
            if ($request->has('repeat') && $request->boolean('repeat') && !$request->has('id')) {
                // $rules[''] = [
                //     'repeats' => 'array|nullable',
                //     'repeats.*.products' => 'array|required',
                //     'repeats.*.products.*.name' => 'nullable|string',
                //     'repeats.*.products.*.price' => 'nullable|numeric',
                //     'repeats.*.products.*.product_id' => 'required|integer',
                //     'repeats.*.facility_id' => 'required|numeric',
                //     'repeats.*.product_id' => 'required|integer',
                //     'repeats.*.quantity' => 'required|numeric',
                //     'repeats.*.selected_dates' => 'array|required',
                //     'repeats.*.selected_dates.*' => 'required',
                // ];
                $rules['repeats'] = 'array|required';
                $rules['repeats.*.products'] = 'array|nullable';
                $rules['repeats.*.products.*.name'] = 'nullable|string';
                $rules['repeats.*.products.*.price'] = 'nullable|numeric';
                $rules['repeats.*.products.*.product_id'] = 'nullable|integer';
                $rules['repeats.*.facility_id'] = 'required|numeric';
                $rules['repeats.*.quantity'] = 'nullable|numeric';
                $rules['repeats.*.selected_dates'] = 'array|required';
                $rules['repeats.*.selected_dates.*'] = 'required';
                $rules['repeats.*.start_time'] = 'date_format:H:i:s';
                $rules['repeats.*.end_time'] = 'required|date_format:H:i:s|after:repeats.*.start_time';
                if($isEnableOverNight){
                    $rules['repeats.*.end_time'] = 'required|date_format:H:i:s';
                }
            }
            $update = false;
            if ($request->has('id')) {
                $update = true;
                $rules['order_id'] = 'exists:orders,id';
            }

            $rules['first_name'] = 'required|string';
            $rules['last_name'] = 'string|nullable';
            $rules['mobile'] = 'required|string';
            $rules['dob'] = 'nullable|date_format:Y-m-d';
            $rules['email'] = 'email';
            $rules['gender'] = 'string';
            $rules['country_id'] = 'nullable|exists:countries,id';
            $rules['profile_image'] = 'image|dimensions:max_width=3000,max_height=3000|max:5000';
            $rules['venue_service_id'] = 'exists:venue_services,id';
            $customMessages = [
                'profile_image.dimensions' => config('constants.img_3000'),
                'image.uploaded' => config('constants.img_error_lg'),
                'repeats.*.selected_dates.*' => 'Booking date is required.',
                'repeats.*.end_time.after' => 'End time should be greater the start time',
            ];
            $api_key = null;
            $icpData = null;
            $isIcpEnabled = $this->venue->enable_icp;

            if($isIcpEnabled){
                $rules['id_proof_type_id'] =  'required|integer|in:32,33';
                $rules['id_proof_number'] = 'required';
                $rules['country_id'] = 'required';
                $rules['dob'] = 'required';
            }
            if ($validate = $this->validationError($request, $rules, $customMessages)) {
                return $validate;
            }

            $assignedFacility = Facility::where(['id' => $request->input('facility_id'), 'venue_id' => $this->venueId])->count();

            if ($assignedFacility === 0) {
                return response()->json(['status' => false, 'message' => 'Facility ' . config('constants.not_found'), 'data' => null], Response::HTTP_NOT_FOUND);
            }

            $assignedVenueService = VenueService::where(['id' => $request->input('venue_service_id'), 'venue_id' => $this->venueId])->count();

            if ($assignedVenueService === 0) {
                return response()->json(['status' => false, 'message' => 'Venue service ' . config('constants.not_found'), 'data' => null], Response::HTTP_NOT_FOUND);
            }


            // if icp enabled validate data with icp as well
            if($isIcpEnabled){
                $api_key = VenueIntegrations::where('venue_id', $this->venueId)->first();
                $idProofNumber = $request->id_proof_number;
                $idProofType = $request->id_proof_type_id == 32?'eid':'passport';
                if ($request->has('country_id')) {
                    $country = Country::where(['id' => $request->input('country_id')])->first();
                    if ($country) {
                        $nationality = $country->dcd_country_id;
                    }
                }else if($request->has('nationality')){
                    $country = Country::where(['name' => $request->nationality])->first();
                    if ($country) {
                        $nationality = $country->dcd_country_id;
                    }
                }else{
                    $nationality = null;
                }
                $icpData = DCDService::validateData($api_key,$idProofNumber,$idProofType,$request->dob,$nationality,'bookingform');
                if(! $icpData){
                    return response()->json(['status' => false, 'message' => 'Details mismatch', 'data' => null], Response::HTTP_CONFLICT);
                }
                [$fist_name, $last_name] = $this->getFirstLastName($icpData->fullNameEn);
                $request->merge(['name' =>  $fist_name.' '.$last_name]);
                $request->merge(['first_name' => $fist_name]);
                $request->merge(['last_name' =>  $last_name]);
                if(isset($icpData->eid)){
                    $request->merge(['idn' => $icpData->eid]);
                }
                if(isset($icpData->dcd_person_id)){
                    $request->merge(['dcd_person_id' => $icpData->dcd_person_id]);
                }
                if(isset($icpData->religion) && $icpData->religion){
                    $request->merge(['religion' => $icpData->religion]);
                }
                if(isset($icpData->gender) && $icpData->gender){
                    $gender =  $icpData->gender ==1?'Male':'Female';
                    $request->merge(['gender' =>$gender]);
                }

                if(isset($request->addOnCustomers) && count($request->addOnCustomers) > 0){
                    $updatedAddOnCustomers = [];
                    foreach($request->addOnCustomers as $key => $addOnCustomer){
                        if(!isset($addOnCustomer['id_proof_number']) || !isset($addOnCustomer['id_proof_type_id']) || !isset($addOnCustomer['country_id']) || !isset($addOnCustomer['dob']) ){
                            return response()->json(['status' => false, 'message' => 'Please fill all the required fields customer: '.($key+2), 'data' => null], Response::HTTP_CONFLICT);
                        }
                        $idProofNumber = $addOnCustomer['id_proof_number'];
                        $idProofType = $addOnCustomer['id_proof_type_id'] == 32?'eid':'passport';
                        $dob = $addOnCustomer['dob'];
                        $country = Country::where(['id' => $addOnCustomer['country_id']])->first();
                        $nationality = null;
                        if ($country) {
                            $nationality = $country->dcd_country_id;
                        }
                        if(! $nationality){
                            return response()->json(['status' => false, 'message' => 'Nationality not found customer: '.($key+2), 'data' => null], Response::HTTP_CONFLICT);
                        }
                        $icpDataAdon = DCDService::validateData($api_key,$idProofNumber,$idProofType,$dob,$nationality,'bookingform');
                        if(! $icpDataAdon){
                            return response()->json(['status' => false, 'message' => 'Details mismatch customer: '.($key+2), 'data' => null], Response::HTTP_CONFLICT);
                        }
                        [$fist_name, $last_name] = $this->getFirstLastName($icpDataAdon->fullNameEn);
                        $updatedAddOnCustomers[$key] = $addOnCustomer;
                        $updatedAddOnCustomers[$key]['name'] = $fist_name.' '.$last_name;
                        $updatedAddOnCustomers[$key]['first_name'] = $fist_name;
                        $updatedAddOnCustomers[$key]['last_name'] = $last_name;
                        if(isset($icpDataAdon->eid)){
                            $updatedAddOnCustomers[$key]['idn'] = $icpDataAdon->eid;
                        }
                        if(isset($icpDataAdon->dcd_person_id)){
                            $updatedAddOnCustomers[$key]['dcd_person_id'] = $icpDataAdon->dcd_person_id;
                        }
                        if(isset($icpDataAdon->religion) && $icpDataAdon->religion){
                            $updatedAddOnCustomers[$key]['religion'] = $icpDataAdon->religion;
                        }
                        if(isset($icpDataAdon->gender) && $icpDataAdon->gender){
                            $updatedAddOnCustomers[$key]['gender'] = $icpDataAdon->gender ==1?'Male':'Female';
                        }
                    }
                    $request->merge(['addOnCustomers' => $updatedAddOnCustomers]);
                }
            }



            /** check order is already paid or not */
            if ($request->has('order_id') && $request->order_id) {
                $orderCheck = Order::find($request->order_id);
                if ($orderCheck && $orderCheck->status_id == 4) {
                    return response()->json(['status' => false, 'message' => 'Booking is already paid, Please refresh the page', 'data' => null], Response::HTTP_CONFLICT);
                }
            }
            DB::beginTransaction();
            $participantCount = $request->attendance_count;
            if ($request->has('attendance_count') && $request->attendance_count > 1 && $request->has('attendance_customer') && is_array($request->attendance_customer)) {
                $participantCount = 1;
                $rules['attendance_customer'] = 'required|Array';
                $rules['attendance_customer.*.first_name'] = 'required|string';
                $rules['attendance_customer.*.last_name'] = 'string|nullable';
                $rules['attendance_customer.*.mobile'] = 'required|string';
                $rules['attendance_customer.*.email'] = 'email';
                $rules['venue_service_id'] = 'exists:venue_services,id';
                $rules['attendance_customer.*.products'] = "required|array";
                $rules['attendance_customer.*.products.*.name'] = "nullable|string";
                $rules['attendance_customer.*.products.*.price'] = "nullable|string";
                $rules['attendance_customer.*.products.*.product_id'] = "required|integer";
                $rules['attendance_customer.*.products.*.order_item_id'] = "nullable|integer";
                $rules['attendance_customer.*.products.*.quantity'] = "required|numeric";
            }
            if($isIcpEnabled){
                $rules['id_proof_type_id'] =  'required|integer|in:32,33';
                $rules['id_proof_number'] = 'required';
                $rules['country_id'] = 'required';
                $rules['dob'] = 'required';
            }
            $customMessages = [
                'profile_image.dimensions' => config('constants.img_3000'),
                'image.uploaded' => config('constants.img_error_lg')
            ];

            if ($validate = $this->validationError($request, $rules, $customMessages)) {
                return $validate;
            }
            if($request->has('order_id')){
                if(!$request->has('booking_id')){
                    $request->request->add(['booking_id' => $request->id]);
                }
                $facilityAvailability = FacilityHelper::facilityAvailability($request, $request->input('venue_service_id'), $this->venueId);
                if ($facilityAvailability->count() == 0) {
                    return response()->json(['status' => true, 'message' => 'Booking slot not available for selected time', 'data' => $facilityAvailability], Response::HTTP_CONFLICT);
                }
            }
            for ($i = 1; $i <= $participantCount; $i++) {
                $check = false;
                $number = $this->getProductCount($request->input('products'), $check);
                if (!$check) {
                    return response()->json(['status' => false, 'message' => 'Please select at least one Ticket', 'data' => null], Response::HTTP_CONFLICT);
                }
                $request->merge(['attendance_count' => $number]);


                if($request->has('facility_id')){
                    $fac = Facility::find($request->facility_id);
                    if(!$fac){
                        return response()->json(['status' => false, 'message' => 'Facility not found', 'data' => null], Response::HTTP_CONFLICT);
                    }
                    $startTime = $request->input('start_time');
                    $endTime = $request->input('end_time');
                    $currentDuration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                    if ($currentDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $currentDuration += 1;
                    }
                    $endDate = FacilityBookingHelper::getFacilityBookingEndDate($startTime, $endTime, $request->date);
                    if(!$endDate && $currentDuration < $fac->min_booking_time){
                        return response()->json(['status' => false, 'message' => 'Minimum booking time is: ' . $fac->min_booking_time . ' minutes', 'data' => null], Response::HTTP_CONFLICT);
                    }
                    // it will work on overnight case if time duration less than minimum facility time
                    if($endDate && $isEnableOverNight){
                        $carbonEndTime = Carbon::parse($endTime);
                        $carbonStartTime = Carbon::parse($startTime);
                        if ($carbonEndTime->lt($carbonStartTime)) {
                            $carbonEndTime->addDay();
                        }
                        $overAllDuration = $carbonStartTime->diffInMinutes($carbonEndTime);
                        if ($overAllDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                            $overAllDuration += 1;
                        }
                        if ($overAllDuration < $fac->min_booking_time) {
                            return response()->json(['status' => false, 'message' => 'Minimum booking time is: ' . $fac->min_booking_time . ' minutes', 'data' => null], Response::HTTP_CONFLICT);
                        }
                    }
                }

                $facilityBooking = $this->saveBookingOrder($request);
            }
            /** This code will only execute when capacity based booking has guest adding. means multiple order creation */
            if ($participantCount == 1 && is_array($request->input('attendance_customer')) && count($request->input('attendance_customer')) > 0) {
                foreach ($request->attendance_customer as $customers) {
                    $data = (array)$customers;
                    $myRequest = new Request($data);
                    $check1 = false;
                    $number1 = $this->getProductCount($myRequest->input('products'), $check1);
                    if (!$check1) {
                        return response()->json(['status' => false, 'message' => 'Please select at least one Ticket', 'data' => null], Response::HTTP_CONFLICT);
                    }
                    $myRequest->merge(['date' => $request->date, 'start_time' => $request->start_time, 'end_time' => $request->end_time, 'facility_id' => $request->facility_id, 'attendance_count' => $number1]);
                    if (isset($data['id_proof']) && $data['id_proof']) {
                        $myRequest->files->add(['id_proof' => $data['id_proof']]);
                    }
                    if (isset($data['profile_image']) && $data['profile_image']) {
                        $myRequest->files->add(['profile_image' => $data['profile_image']]);
                    }
                    $this->saveBookingOrder($myRequest);
                }
            }
            //            dd(1);
            /** check if KIOSK order and amount 0 then complementary paid */
            if (!$request->has('order_id') && $request->has('is_kiosk')) {
                if ($facilityBooking) {
                    $order = Order::find($facilityBooking->order_id);
                    if ($order && $order->total == 0) {
                        /** Generate invoice*/
                        $invoice = app('App\Http\Controllers\Invoices\InvoiceController')->generateInvoice([$order->id]);
                        if ($invoice && $invoice->id) {
                            $response = $this->kioskInvoicePaid($invoice->id, $order->customer_id);
                            $this->autoCheckInKiosk($invoice->id);
                        } else {
                            DB::rollback();
                            return response()->json(['status' => false, 'message' => "Issue in generating invoice", 'data' => null], Response::HTTP_CONFLICT);
                        }
                    }
                }
            }else if(!$request->has('order_id')){
                if ($facilityBooking) {
                    $order = Order::find($facilityBooking->order_id);
                    if ($order->total == 0 ) {
                        $paymentSvc = new PaymentService();
                        $order = $paymentSvc->createZeroTotalInvoice($order,$request->is_kiosk);
                    }
                }
            }


            if (isset($facilityBooking) && $facilityBooking) {
                $facility = Facility::find($facilityBooking->facility_id);

                $isBaps = B2cConfiguration::where('venue_id',$this->venueId)->where('status_id',1)->first();
                if(isset($isBaps->colors['mode']) && $isBaps->colors['mode'] === 'baps' && config('app.env') === "live"){
                    (new BapsDataController())->bookingDataTransfer($facilityBooking->id,$this->venueId);
                }

                if ($update) {
                    Logging::create([
                        'venue_id' => $this->venueId,
                        'user_id' => $this->userId,
                        'target_id' => $facilityBooking->id,
                        'model' => get_class($facilityBooking),
                        'table' => $facilityBooking->getTable(),
                        'action' => 'Update',
                        'description' => "{$this->user->first_name} {$this->user->last_name} updated facility booking on {$facilityBooking->date} at {$facilityBooking->start_time} in '{$facility->name}'.",
                    ]);
                } else {
                    Logging::create([
                        'venue_id' => $this->venueId,
                        'user_id' => $this->userId,
                        'target_id' => $facilityBooking->id,
                        'model' => get_class($facilityBooking),
                        'table' => $facilityBooking->getTable(),
                        'action' => 'Create',
                        'description' => "{$this->user->first_name} {$this->user->last_name} created facility booking on {$facilityBooking->date} at {$facilityBooking->start_time} in '{$facility->name}'.",
                    ]);
                }
            }
            if($this->venue->enable_email && $request->has('order_id') && $request->boolean('notify_customers') ){
                SendFacilityBookingRescheduleMailJob::dispatch($facilityBooking,$this->venue);
            }
            if($request->has('order_id')){
                SendOperatorFacilityBookingRescheduleMailJob::dispatch($facilityBooking,$this->venue);
            }
            DB::commit();
            $message = $request->has('voucher_sales_id') ? config('constants.update_success') : config('constants.add_success');
            return response()->json(['status' => true, 'message' => $message, 'data' => $facilityBooking], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => $e->getLine()], Response::HTTP_CONFLICT);
        }
    }

    public function getProductCount($products, &$check)
    {
        $count = 0;
        foreach ($products as $product) {
//            if ($product['rental'] == 'true') {
//                $check = true;
//            }
            if($product['rental'] == 'true' || filter_var($product['rental'], FILTER_VALIDATE_BOOLEAN)){
                $check = true;
            }
            if (!isset($product['participant_count'])) {
                $participant_count = $this->getParticipantCount($product['product_id']);
            } else {
                $participant_count = $product['participant_count'] && $product['participant_count'] != 'undefined' ? $product['participant_count'] : 1;
            }
            if($product['rental'] == 'true' || filter_var($product['rental'], FILTER_VALIDATE_BOOLEAN)){
                $count = $count + ((int)$product['quantity'] * (int)$participant_count);
            }
        }
        if (!$count) {
            $count = 1;
        }
        return $count;
    }

    public function getParticipantCount($id)
    {
        $count = Product::find($id);
        return $count ? $count->participant_count : 1;
    }


    function hasOpenProduct($array)
    {
        foreach ($array as $child) {
            if ($child['product_id'] === '0' || $child['product_id'] === null) {
                return true; // Found a child with product_id = 0 or null
            }
        }
        return false; // No child with product_id = 0 or null found
    }

    function hasRentalProduct($array)
    {
        foreach ($array as $child) {
            if ($child['product_id'] !== '0') {
                return true; // Found a child with product_id = 0 or null
            }
        }
        return false; // No child with product_id = 0 or null found
    }

    private function saveRepeatBookingOrder($request)
    {

        $allProducts = $request->input('products');
        $addOn = array_filter($allProducts, function ($o) {
            return $o['rental'] == 'false';
        });

        // overlapping time validation
        foreach ($request->input('repeats') as $key1 => $repeat1) {
            $repeat1 = (object)$repeat1;
            $facilityId = $repeat1->facility_id;
            $startTime1 = Carbon::parse($repeat1->start_time);
            $endTime1 = Carbon::parse($repeat1->end_time);
            foreach ($request->input('repeats') as $key2 => $repeat2) {
                if ($key1 != $key2) {
                    $repeat2 = (object)$repeat2;
                    $startTime2 = Carbon::parse($repeat2->start_time);
                    $endTime2 = Carbon::parse($repeat2->end_time);
                    if (
                        $repeat1->date == $repeat2->date && $repeat1->facility_id == $repeat2->facility_id &&
                        (
                            ($startTime1->gte($startTime2) && $startTime1->lt($endTime2)) ||
                            ($endTime1->gt($startTime2) && $endTime1->lte($endTime2))
                        )
                    ) {
                        throw new \Exception('Repeat Error: Duplicate or overlapping date/time found for ' . Carbon::parse($repeat1->date)->format('d M Y') . ' from ' . Carbon::parse($repeat1->start_time)->format('h:i a') . ' to ' . Carbon::parse($repeat1->end_time)->format('h:i a'));
                    }
                }
            }
        }

        // validation more for open products
        if (!$request->has('repeat_id') && $request->has('repeats') && $request->boolean('repeat')) {
            $this->validateOpenBaseProduct($request);
        }
        $adds = [];
        $facilityId = $request->input('facility_id');
        $perCapacity = Facility::where('id', $facilityId)->pluck('per_capacity')->first();

        $venueServiceConfiguration = VenueServiceConfiguration::where(['venue_service_id' => $request->venue_service_id, 'status_id' => 1])->first();
        if ($request->has('deleted_products')) {
            $deletedProducts = json_decode($request->input('deleted_products'));
            ProductInventorySales::whereIn('order_item_id', $deletedProducts)->update(['status_id' => 2]);
        }

        if ($request->has('id')) {
            $this->validateProductChange($request);
        }
        // Tag customer on first issue
        $customer = $this->createCustomer($request);

        $discount = null;
        if ($request->has('promotion_code')) {
            $discount = DiscountHelper::getBenefits($this->venueId, $request->input('promotion_code'), 'promotion', $customer->mobile);
            $discount->type = 'promotion';
        }
        if ($request->has('card_number')) {
            $discount = DiscountHelper::getBenefits($this->venueId, $request->input('card_number'), 'membership', $customer->mobile);
            $discount->type = 'membership';
        }
        $invId = $request->has('invoice_id') ? $request->invoice_id : null;
        $repeatBookingId = NULL;
        if (!$request->has('repeat_id')) {
            $invoiceController = (new InvoiceController());
            $invoice = InvoiceHelper::createOrUpdateInvoice([
                'invoice_seq_no' => $invoiceController->generateInvoiceSequence('I'),
                'invoice_date' => $request->input('date'),
                'price' => 0,
                'tax' => 0,
                'total' => 0,
                'venue_id' => $this->venueId,
                'customer_id' => $customer->id,
                'status_id' => 5,
                'invoice_status_id' => 11,
            ], $invId);
            $meta = $request->input('repeats');

            $facilityBookingRepeat = new FacilityBookingRepeat;
            $facilityBookingRepeat->meta = $meta;
            $facilityBookingRepeat->repeat_count = 1;
            $facilityBookingRepeat->save();
            $repeatBookingId = $facilityBookingRepeat->id;
            $oldOrderId = null;


            foreach ($request->input('repeats') as $key => $repeat) {
                $selected_dates = $repeat['selected_dates'];
                $daysCount = count($selected_dates);
                $productForRepeat = [];
                $facilityId = $repeat['facility_id'];
                if ($this->hasOpenProduct($allProducts)) {
                    $tempProducts = [];
                    foreach ($allProducts as $item) {
                        $product = $this->saveOpenProduct((object)$item);
                        $openCount = collect($allProducts)->where('product_id', 0)->where('rental', 'true')->count();
                        $productCategory = ProductCategory::updateOrCreate(['product_id' => $product->id, 'category_id' => $product->category_id]);
                        $assignQty = $item['quantity'] > $daysCount ? $daysCount : 1;
                        if ($item['rental'] == 'true') {
                            $startTime = $repeat['start_time'];
                            $endTime = $repeat['end_time'];
                            $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                            // $rentalDuration = (($duration / $openCount) / $item['quantity']) * $daysCount;
                            $rentalDuration = $duration;
                            $rentalProduct = new RentalProduct;
                            $rentalProduct->product_id = $product->id;
                            $rentalProduct->duration = $rentalDuration;
                            $rentalProduct->status_id = 14;
                            $rentalProduct->venue_service_id = $request->input('venue_service_id');
                            $rentalProduct->save();
                        }

                        foreach ($selected_dates as $date) {
                            if ($item['rental'] == 'true') {
                                $tempProducts[$date][] = (object)[
                                    'id' => $product->id,
                                    'duration' => $rentalDuration,
                                    'is_full_day' => 0,
                                    'image_path' => NULL,
                                    'product_type_id' => 6,
                                    'name' => $product->name,
                                    'seasonal_pricing_id' => NULL,
                                    'category_id' => $product->category_id,
                                    'total_tax_amount' => $product->tax_amount,
                                    'tax_amount' => $product->tax_amount,
                                    'product_price' => $product->price,
                                    'price' => $product->price, //$quantityOfProduct,
                                    'quantity' => 1, //$quantityOfProduct,
                                    'is_repeatable' => 1,
                                ];
                            }
                        }
                    }
                    $productForRepeat = $tempProducts;
                } else {
                    foreach ($selected_dates as $date) {
                        $startTime = $repeat['start_time'];
                        $endTime = $repeat['end_time'];
                        $repeatEndDates = [];
                        $carbonEndTime = Carbon::parse($repeat['end_time']);
                        $carbonStartTime = Carbon::parse($repeat['start_time']);
                        if ($carbonEndTime->lt($carbonStartTime)) {
                            $repeatEndDates = (new RepeatBookingController())->getRepeatEndDates([$date]);
                        }
                        if (count($repeatEndDates) > 0) {
                            $endTime2 = $endTime;
                            $request1 = new Request(['date' => $date, 'facility_id' => $facilityId, 'same_day' => 1]);
                            $facilityController = (new FacilityController());
                            $sameDayTiming = $facilityController->getFacilityTimings($request1);
                            if (!json_decode($sameDayTiming->getContent())->status) {
                                throw new Exception('Couldn\'t get same day end time');
                            } else {
                                $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                            }

                            $request2 = new Request(['date' => $repeatEndDates[0], 'facility_id' => $facilityId, 'same_day' => 1]);
                            $nextDayTiming = $facilityController->getFacilityTimings($request2);
                            if (!json_decode($nextDayTiming->getContent())->status) {
                                throw new Exception('Couldn\'t get next day start time');
                            } else {
                                $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                            }
                        }

                        $get = FacilityBookingHelper::getProductWithDifferentTimingTemplates($facilityId, $startTime, $endTime, [$date]);

                        if (count($repeatEndDates) > 0) {
                            $productObj2 = FacilityBookingHelper::getProductWithDifferentTimingTemplates($facilityId, $startTime2, $endTime2, $repeatEndDates);
                            $get['products'] = FacilityBookingHelper::mergeProducts($get['products'], $productObj2['products']);
                        }
                        $productForRepeat[$date] = $get['products'];
                    }
                }

                foreach ($selected_dates as $index => $s_date) {
                    if ($key == 0 && $index == 0 && count($addOn) > 0) {
                        foreach ($addOn as $add) {
                            $productForRepeat[$s_date][] = $add;
                        }
                    }
                    $reqArr = [
                        'repeats' => [$repeat],
                        'order_id' => isset($repeat['order_id']) && $repeat['order_id'] ? $repeat['order_id'] : null,
                        'date' => $s_date,
                        'facility_id' => $facilityId,
                        'start_time' => $repeat['start_time'],
                        'end_time' => $repeat['end_time'],
                        'order_notes' => $request->has('order_notes') ? $request->input('order_notes') : null,
                        'products' => $productForRepeat[$s_date],
                        'venue_service_id' => $request->input('venue_service_id'),
                        'initial_order_id' => $oldOrderId,
                    ];
                    if ($request->has('sales_team_id')) {
                        $reqArr['sales_team_id'] = $request->input('sales_team_id');
                    }
                    if ($request->has('is_kiosk')) {
                        $reqArr['is_kiosk'] = $request->input('is_kiosk');
                    }

                    $newRequest = new Request($reqArr);

                    $order = $this->createOrder($newRequest, $customer->id, $discount, $invoice);
                    $order = Order::find($order->id);

                    if ($key == 0 && $index == 0) {
                        $oldOrderId = $order->id;
                    }

                    $request->merge(['products' => $newRequest->products]);

                    $invoiceController->checkOrderAlreadyExistInInvoice([$order->id]);
                    InvoiceHelper::createOrUpdateInvoiceItem([
                        'order_id' => $order->id,
                        'invoice_id' => $invoice->id,
                        'price' => $order->price,
                        'tax' => $order->tax,
                        'total' => $order->total,
                        'status_id' => $order->status_id,
                    ]);

                    $startTime = $repeat['start_time'];
                    $endTime = $repeat['end_time'];

                    $products = collect($newRequest->input('products'))->sortBy('product_id');
                    $productIds = OrderItem::where('order_id', $order->id)->where('status_id', '!=', 2)->get()->pluck('product_id');
                    $rentalProducts = DB::table('rental_products as rp')->whereIn('rp.product_id', $productIds)->orderBy('rp.product_id')->get();
                    $rentalProductIds = $rentalProducts->pluck('product_id')->toArray();
                    $totalDuration = 0;
                    // return $rentalProducts;
                    foreach ($rentalProducts as $rentalProduct) {
                        $quantity = $products->firstWhere('id', $rentalProduct->product_id);
                        $totalDuration += $rentalProduct->duration * $quantity->quantity;
                        if ($rentalProduct->duplicate_booking == 1 && $perCapacity) {
                            $isPossible = FacilityBookingHelper::checkDuplicateBookingIsPossible($request, $perCapacity, $rentalProduct, $this->venueId);
                            if (!$isPossible['status']) {
                                throw new \Exception($isPossible['message']);
                            }
                        }
                    }

                    if (!$perCapacity) {

                        $duration2 = null;
                        $startTime = $repeat['start_time'];
                        $endTime = $repeat['end_time'];
                        $repeatEndDates = [];
                        $carbonEndTime = Carbon::parse($repeat['end_time']);
                        $carbonStartTime = Carbon::parse($repeat['start_time']);
                        if ($carbonEndTime->lt($carbonStartTime)) {
                            $repeatEndDates = (new RepeatBookingController())->getRepeatEndDates([$date]);
                        }
                        if (count($repeatEndDates) > 0) {
                            $endTime2 = $endTime;
                            $request1 = new Request(['date' => $date, 'facility_id' => $facilityId, 'same_day' => 1]);
                            $facilityController = (new FacilityController());
                            $sameDayTiming = $facilityController->getFacilityTimings($request1);
                            if (!json_decode($sameDayTiming->getContent())->status) {
                                throw new Exception('Couldn\'t get same day end time');
                            } else {
                                $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                            }

                            $request2 = new Request(['date' => $repeatEndDates[0], 'facility_id' => $facilityId, 'same_day' => 1]);
                            $nextDayTiming = $facilityController->getFacilityTimings($request2);
                            if (!json_decode($nextDayTiming->getContent())->status) {
                                throw new Exception('Couldn\'t get next day start time');
                            } else {
                                $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                            }
                            $duration2 = Carbon::parse($startTime2)->diffInMinutes(Carbon::parse($endTime2));
                        }


                        $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                        if ($duration2) {
                            $duration = $duration + $duration2;
                        }

                        if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                            $duration += 1;
                        }
                        if ($totalDuration != $duration) {
                            DB::rollback();
                            throw new \Exception("Selected duration ($duration min) doesn't match added rental product(s) ($totalDuration min)");
                        }
                    }

                    $openProductRentals = DB::table('rental_products as rp')
                        ->select(
                            'oi.id',
                            'rp.product_id',
                            'rp.duration',
                            DB::raw("(oi.quantity) as quantity"),
                            'p.price',
                            'p.tax_amount',
                            'p.total_price'
                        )
                        ->join('product_categories as pc', 'pc.product_id', '=', 'rp.product_id')
                        ->join('categories as c', 'c.id', '=', 'pc.category_id')
                        ->join('products as p', 'p.id', '=', 'rp.product_id')
                        ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                        ->where('c.name', 'open_product')->whereIn('pc.product_id', $productIds)->orderBy('pc.product_id')->get();

                    $repeatBookingWithOpenProduct = $openProductRentals->count() > 0;



                    $repeats = (object)$repeat;
                    $overLapping = false;
                    $currentDuration = Carbon::parse($repeats->start_time)->diffInMinutes(Carbon::parse($repeats->end_time));
                    if ($currentDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $currentDuration += 1;
                    }

                    $currentProductIds = [];
                    $currentProducts = isset($repeats->products) ? $repeats->products : [];
                    foreach ($currentProducts as $key => $currentProduct) {
                        $currentProduct = json_decode($currentProduct);
                        $currentProductIds[] = $currentProduct->id;

                        if (($key = array_search($currentProduct->id, $rentalProductIds)) !== false) {
                            unset($rentalProductIds[$key]);
                        }
                    }
                    // log::info($currentProductIds);
                    if ($repeatBookingWithOpenProduct) {
                        $rentalProducts = $openProductRentals;
                    } else {
                        $rentalProducts = DB::table('rental_products as rp')
                            ->select(
                                'oi.id',
                                'fr.weekdays',
                                'fr.start_time',
                                'fr.end_time',
                                'rp.product_id',
                                'rp.duration',
                                DB::raw("($currentDuration/rp.duration) as quantity"),
                                'p.price',
                                'p.tax_amount',
                                'p.total_price'
                            )
                            ->join('facility_rental_products as frp', 'frp.rental_product_id', '=', 'rp.id')
                            ->join('facility_rentals as fr', 'frp.facility_rental_id', '=', 'fr.id')
                            ->join('products as p', 'p.id', '=', 'rp.product_id')
                            ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                            ->whereIn('rp.product_id', $currentProductIds)->where('fr.facility_id', $facilityId)
                            ->where(function ($query) use ($repeats) {
                                $query->where(function ($query) use ($repeats) {
                                    $query->where('fr.start_time', '<=', $repeats->start_time);
                                    $query->where('fr.end_time', '>=', $repeats->end_time);
                                });
                            })->where(['fr.status_id' => 1, 'rp.status_id' => 1, 'frp.status_id' => 1, 'oi.order_id' => $order->id])
                            ->groupBy('rp.product_id')->orderBy('fr.start_time', 'asc')
                            ->orderBy('rp.duration', 'desc')->get();
                        if ($rentalProducts->count() == 0) {
                            $overLapping = false;
                            $rentalProducts = DB::table('rental_products as rp')
                                ->select(
                                    'oi.id',
                                    'fr.weekdays',
                                    'fr.start_time',
                                    'fr.end_time',
                                    'rp.product_id',
                                    'rp.duration',
                                    DB::raw("round(
                                           if('$endTime' > fr.end_time,
                                        ((TIME_TO_SEC(fr.end_time) - TIME_TO_SEC(if('$startTime' < fr.start_time,fr.start_time,'$startTime')))/60)/rp.duration,
                                        ((TIME_TO_SEC('$endTime') - TIME_TO_SEC(fr.start_time))/60)/rp.duration
                                    ),
                                    2
                                    ) as quantity"),
                                    'p.price',
                                    'p.tax_amount',
                                    'p.total_price'
                                )
                                ->join('facility_rental_products as frp', 'frp.rental_product_id', '=', 'rp.id')
                                ->join('facility_rentals as fr', 'frp.facility_rental_id', '=', 'fr.id')
                                ->join('products as p', 'p.id', '=', 'rp.product_id')
                                ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                                ->whereIn('rp.product_id', $currentProductIds)->where('fr.facility_id', $facilityId)
                                ->where(function ($query) use ($repeats) {
                                    $query->orWhere(function ($query) use ($repeats) {
                                        $query->where(function ($query) use ($repeats) {
                                            $query->whereRaw("fr.start_time < '$repeats->start_time' and fr.end_time > '$repeats->start_time'");
                                        })->orWhere(function ($query) use ($repeats) {
                                            $query->whereRaw("fr.start_time < '$repeats->end_time' and fr.end_time > '$repeats->end_time'");
                                        })->orWhere(function ($query) use ($repeats) {
                                            $query->orWhereRaw("fr.start_time between '$repeats->start_time' and '$repeats->end_time'");
                                        })->orWhere(function ($query) use ($repeats) {
                                            $query->orWhereRaw("fr.end_time between '$repeats->start_time' and '$repeats->end_time'");
                                        });
                                    });
                                })->where(['fr.status_id' => 1, 'rp.status_id' => 1, 'frp.status_id' => 1, 'oi.order_id' => $order->id])
                                ->groupBy('rp.product_id')->orderBy('fr.start_time', 'asc')
                                ->orderBy('fr.start_time', 'desc')->orderBy('rp.duration', 'desc')->get();
                        }
                    }
                    $weekdays = Weekday::all();
                    //                $repeatData = [];
                    //                $repeatDatesRow = [];

                    $dayOfWeek = Carbon::parse($s_date)->englishDayOfWeek;
                    $bitValue = $weekdays->firstWhere('name', $dayOfWeek)->bit_value;
                    $value = (array)['date' => $s_date, 'start_time' => $repeat['start_time'], 'end_time' => $repeat['end_time'], 'duration' => $duration];

                    $newRequest->merge($value);
                    $facilityBooking = $this->createFacilityBooking($newRequest, $order, $repeatBookingId);

                    if (isset($addOn) && count($addOn)) {
                        $adOndata = array_values($addOn);
                        $addOnProductIds = array_column($adOndata, 'product_id');
                        $orderItems = OrderItem::where('order_id', $order->id)->whereNotIn('product_id', $addOnProductIds)->get();
                    } else {
                        $orderItems = OrderItem::where('order_id', $order->id)->get();
                    }
                    foreach ($orderItems as $rental) {
                        $facilityBookingItem = FacilityBookingItem::updateOrCreate(['order_item_id' => $rental->id, 'facility_booking_id' => $facilityBooking->id]);
                        $facilityBookingItem->quantity = $rental->quantity;
                        if ($rental->price > 0) {
                            $facilityBookingItem->price = $rental->price;
                        }
                        if ($rental->total > 0) {
                            $facilityBookingItem->total = $rental->total;
                        }
                        if ($rental->tax > 0) {
                            $facilityBookingItem->tax = $rental->tax;
                        }
                        $facilityBookingItem->save();
                    }
                    GroupCustomer::where('order_id', $order->id)->delete();
                    if (!$venueServiceConfiguration->is_golf_enabled) {
                        $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id]);

                        if ($request->has('addOnCustomers') && is_array($request->input('addOnCustomers')) && count($request->input('addOnCustomers')) > 0) {
                            foreach ($request->input('addOnCustomers') as $addOns) {
                                $data_for_request = (array)$addOns;
                                $myRequest = new Request($data_for_request);
                                $addOns = $this->createCustomer($myRequest);

                                $addOnOrderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'customer_id' => $addOns->id, 'status_id' => 1])->first();
                                if (!empty($addOnOrderGroupCustomer)) {
                                    $addOnOrderGroupCustomer->customer_id = $addOns->id;
                                    $addOnOrderGroupCustomer->order_id = $order->id;
                                } else {
                                    $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id]);
                                }
                                $addOnOrderGroupCustomer->save();
                            }
                        }
                    } else {
                        $orderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'status_id' => 1])->first();
                        if (!empty($orderGroupCustomer)) {
                            $orderGroupCustomer->customer_id = $customer->id;
                            $orderGroupCustomer->order_id = $order->id;
                        } else {
                            $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id]);
                        }

                        if ($request->has('addOnCustomers') && is_array($request->input('addOnCustomers')) && count($request->input('addOnCustomers')) > 0) {
                            foreach ($request->input('addOnCustomers') as $addOns) {
                                $data_for_request = (array)$addOns;
                                $myRequest = new Request($data_for_request);
                                $addOns = $this->createCustomer($myRequest);
                                // log::info($myRequest);
                                // log::info(json_encode($addOns));
                                $adds[] = $addOns->id;
                                $addOnOrderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'customer_id' => $addOns->id, 'status_id' => 1])->first();
                                if (!empty($addOnOrderGroupCustomer)) {
                                    $addOnOrderGroupCustomer->customer_id = $addOns->id;
                                    $addOnOrderGroupCustomer->order_id = $order->id;
                                } else {
                                    $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id]);
                                }
                                $facilityBookingId = isset($facilityBooking) && $facilityBooking->id ? $facilityBooking->id : NULL;
                                if ($venueServiceConfiguration->is_golf_enabled) {
                                    $addOnOrderGroupCustomer->facility_booking_id = $facilityBookingId;
                                }
                                $addOnOrderGroupCustomer->save();
                            }
                        }
                    }
                    /** save facility_booking_id for golf group customers */
                    $facilityBookingId = isset($facilityBooking) && $facilityBooking->id ? $facilityBooking->id : NULL;
                    if ($venueServiceConfiguration->is_golf_enabled) {
                        $orderGroupCustomer->facility_booking_id = $facilityBookingId;
                    }
                    $orderGroupCustomer->save();

                    if ($facilityBooking->facility_booking_duplicate_id == null && $perCapacity == 1) {
                        /** save group customer for golf booking in duplicate booking */
                        $customer_id = null;
                        if ($venueServiceConfiguration->is_golf_enabled) {
                            $customer_id = $customer->id;
                        }
                        FacilityBookingHelper::saveDuplicateBooking($productIds, $request, $facilityBooking, $customer_id, $adds);
                    }


                    if ($facilityBooking->customer_type == 'corporate' && $request->has('company_sale_id')) {
                        CompanySaleOrder::updateOrCreate(['company_sale_id' => $request->input('company_sale_id'), 'order_id' => $order->id]);
                        $orderPayment = new OrderPayment;
                        $orderPayment->order_id = $order->id;
                        $orderPayment->payment_method_id = 5;
                        $orderPayment->total = $order->total;
                        $tax = $order->tax;
                        $orderPayment->tax = $tax;
                        $orderPayment->amount = $order->total - $tax;
                        $orderPayment->status_id = 1; // paid
                        $orderPayment->save();
                        $creditOrder = new CreditOrder;
                        $creditOrder->order_payment_id = $orderPayment->id;
                        $creditOrder->order_id = $order->id;
                        $creditOrder->credit = $order->total;
                        $order->order_seq_no = $this->generateSequence('R', $order->id);
                        $creditOrder->status_id = 1;
                        $creditOrder->save();
                        $order->status_id = 4;
                        $facilityBooking->status_id = 1;
                        $order->paid_at = Carbon::now('UTC')->format('Y-m-d H:i:s');
                        $order->paid_by = $this->userId;
                        $order->save();
                        $facilityBooking->save();
                    }
                }
            }

            InvoiceHelper::updateInvoicePrice($invoice->id);
        } else if ($request->has('repeat_id')) {
            $facilityBooking = FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->first();

            if ($request->has('id_proof_type_id')) {
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof_type_id' => $request->input('id_proof_type_id')]);
            }
            if ($request->has('id_proof_number')) {
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof_number' => $request->input('id_proof_number')]);
            }
            if ($request->hasFile('id_proof')) {
                $file = $this->fileUpload($request->file('id_proof'), 'facility/booking/id', null);
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof' => $file->file_path]);
            }
            if ($request->hasFile('profile_image')) {
                $file = $this->fileUpload($request->file('profile_image'), 'facility/booking/image', null);
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['image_path' => $file->file_path]);
            }
        }

        if (isset($order) && $order) {
            if (!$request->has('invoice_id') && $this->venue->enable_email) {
                if ($request->has('is_kiosk')) {
                    if ($order->total > 0) {
                        OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                    }
                } else {
                    if ($this->venue->salesConfiguration && isset($this->venue->salesConfiguration->enable_email)) {
                        if ($this->venue->salesConfiguration->enable_email == 1) {
                            OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                        }
                    } else {
                        OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                    }
                }
            }
        }
        return $facilityBooking;
    }


    private function saveBookingOrder($request)
    {
        $fullDay = false;
        if ($request->has('repeats') && $request->boolean('repeat')) {
            return $this->saveRepeatBookingOrder($request);
        }
        $adds = [];
        $facilityId = $request->input('facility_id');
        $perCapacity = Facility::where('id', $facilityId)->pluck('per_capacity')->first();

        $venueServiceConfiguration = VenueServiceConfiguration::where(['venue_service_id' => $request->venue_service_id, 'status_id' => 1])->first();
        if ($request->has('deleted_products')) {
            $deletedProducts = json_decode($request->input('deleted_products'));
            ProductInventorySales::whereIn('order_item_id', $deletedProducts)->update(['status_id' => 2]);
        }

        if ($request->has('id')) {
            $this->validateProductChange($request);
        }
        // Tag customer on first issue
        $customer = $this->createCustomer($request);

        if($request->has('dcd_person_id')){
            VenueCustomer::where(['venue_id' => $this->venueId,'customer_id' => $customer->id])->update(['dcd_person_id' => $request->input('dcd_person_id')]);
        }

        $discount = null;
        if ($request->has('promotion_code')) {
            $discount = DiscountHelper::getBenefits($this->venueId, $request->input('promotion_code'), 'promotion', $customer->mobile);
            $discount->type = 'promotion';
        }
        if ($request->has('card_number')) {
            $discount = DiscountHelper::getBenefits($this->venueId, $request->input('card_number'), 'membership', $customer->mobile);
            $discount->type = 'membership';
        }

        $order = $this->createOrder($request, $customer->id, $discount);
        $startTime = $request->input('start_time');
        $endTime = $request->input('end_time');
        $products = collect($request->input('products'))->sortBy('product_id');
        // $fullDay = $products->max('is_full_day') ?? false;
        $fullDayFilter = $products->filter(function ($item) {
            return isset($item['is_full_day']) && ($item['is_full_day'] === 1 || $item['is_full_day'] === "1");
        });

        if ($fullDayFilter->count() > 0) {
            $fullDay = true;
            $filteredData = $products->filter(function ($item) {
                return $item['rental'] === 'true';
            });
            if ($filteredData->count() > 1) {
                throw new \Exception('Cannot add multiple rental products with a Full day product');
            }
        }
        $productIds = OrderItem::where('order_id', $order->id)->where('status_id', '!=', 2)->get()->pluck('product_id');

        // $productTypeID = ProductType::where('name', 'Facility')->pluck('id')->first();
        // $venueServiceConfiguration = VenueServiceConfiguration::firstOrCreate(['venue_service_id' => $request->input('venue_service_id'), 'product_type_id' => $productTypeID]);

        $rentalProducts = DB::table('rental_products as rp')->whereIn('rp.product_id', $productIds)->orderBy('rp.product_id')->get();
        $rentalProductIds = $rentalProducts->pluck('product_id')->toArray();

        $totalDuration = 0;

        foreach ($rentalProducts as $rentalProduct) {
            $quantity = $products->firstWhere('product_id', $rentalProduct->product_id);
            $totalDuration += $rentalProduct->duration * $quantity['quantity'];
            if ($rentalProduct->duplicate_booking == 1 && $perCapacity) {
                $isPossible = FacilityBookingHelper::checkDuplicateBookingIsPossible($request, $perCapacity, $rentalProduct, $this->venueId);
                if (!$isPossible['status']) {
                    throw new \Exception($isPossible['message']);
                }
            }
        }
        if (!$perCapacity) {
            if ($request->has('repeats') && $fullDay) {
                throw new \Exception("Cannot make Full day long term booking");
            }
            $endDate = FacilityBookingHelper::getFacilityBookingEndDate($startTime, $endTime, $request->date);
            if (!$endDate) {
                $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
            } else {
                if ($endDate) {
                    $endTime2 = $endTime;
                    $request1 = new Request(['date' => $request->input('date'), 'facility_id' => $request->facility_id, 'same_day' => 1]);
                    $facilityController = (new FacilityController());
                    $sameDayTiming = $facilityController->getFacilityTimings($request1);
                    if (!json_decode($sameDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get same day end time');
                    } else {
                        $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                    }

                    $request2 = new Request(['date' => $endDate, 'facility_id' => $facilityId, 'same_day' => 1]);
                    $nextDayTiming = $facilityController->getFacilityTimings($request2);
                    if (!json_decode($nextDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get next day start time');
                    } else {
                        $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                    }
                    $duration1 = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                    $duration2 = Carbon::parse($startTime2)->diffInMinutes(Carbon::parse($endTime2));
                    $duration = $duration1 + $duration2;
                }
            }

            if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                $duration += 1;
            }


            if ($request->has('repeats')) {
                $repeats = [];
                $count = 0;
                $greatestDate = Carbon::now();
                $duration = 0;

                foreach ($request->input('repeats') as $key => $repeat) {
                    $repeat = (object)$repeat;
                    $currentDuration = Carbon::parse($repeat->start_time)->diffInMinutes(Carbon::parse($repeat->end_time));
                    if ($currentDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $currentDuration += 1;
                    }
                    $currentCount = 0;
                    if (!isset($repeat->selected_dates) || (isset($repeat->selected_dates) && count($repeat->selected_dates) == 0)) {
                        throw new \Exception('Repeat Error: No selected dates for the row ' . ($key + 1));
                    }
                    foreach ($repeat->selected_dates as $date) {
                        $isRepeatExists = 1;
                        if ($request->has('order_id')) {
                            $isRepeatExists = FacilityBooking::where(['order_id' => $request->input('order_id'), 'date' => $date, 'start_time' => $repeat->start_time, 'end_time' => $repeat->end_time])->count();
                        }
                        if ($isRepeatExists > 0) {
                            $count += 1;
                            $currentCount += 1;
                            $greatestDate = Carbon::parse($date)->gt($greatestDate) ? Carbon::parse($date) : $greatestDate;
                            $value = (array)['date' => $date, 'start_time' => $repeat->start_time, 'end_time' => $repeat->end_time];
                            $repeats[] = $value;
                        }
                    }
                    $newDuration = $currentDuration * $currentCount;
                    $duration += $newDuration;
                }
            }
            $request->request->add(['duration' => $duration]);
            if ($totalDuration != $duration && !$fullDay) {
                DB::rollback();
                throw new \Exception("Selected duration ($duration min) doesn't match added rental product(s) ($totalDuration min)");
                // return response()->json(['status' => false, 'message' => "Selected duration ($duration min) doesn't match added rental product(s) ($totalDuration min)", 'data' => null], Response::HTTP_CONFLICT);
            }

            if ($fullDay && !FacilityBookingHelper::validateFullDayBooking($request)) {
                DB::rollBack();
                throw new \Exception("Full day booking not possible");
            }
        } else {
            $facility = Facility::where('id', $facilityId)->first();
            $request->request->add(['duration' => $facility->min_booking_time]);
        }

        $repeatBookingId = NULL;
        $fac = Facility::find($facilityId);
        if ($request->has('repeats') && $request->boolean('repeat') && !$request->has('repeat_id')) {

            foreach ($repeats as $key1 => $repeat1) {
                $repeat1 = (object)$repeat1;
                $startTime1 = Carbon::parse($repeat1->start_time);
                $endTime1 = Carbon::parse($repeat1->end_time);
                foreach ($repeats as $key2 => $repeat2) {
                    if ($key1 != $key2) {
                        $repeat2 = (object)$repeat2;
                        $startTime2 = Carbon::parse($repeat2->start_time);
                        $endTime2 = Carbon::parse($repeat2->end_time);
                        if ($repeat1->date == $repeat2->date && $startTime1->lt($endTime2) && $endTime1->gt($startTime2)) {
                            throw new \Exception('Repeat Error: Duplicate or overlapping date/time found for ' . Carbon::parse($repeat1->date)->format('d M Y') . ' from ' . Carbon::parse($repeat1->start_time)->format('h:i a') . ' to ' . Carbon::parse($repeat1->end_time)->format('h:i a'));
                        }
                    }
                }
            }

            $openProductRentals = DB::table('rental_products as rp')
                ->select(
                    'oi.id',
                    'rp.product_id',
                    'rp.duration',
                    DB::raw("(oi.quantity/$count) as quantity"),
                    'p.price',
                    'p.tax_amount',
                    'p.total_price'
                )
                ->join('product_categories as pc', 'pc.product_id', '=', 'rp.product_id')
                ->join('categories as c', 'c.id', '=', 'pc.category_id')
                ->join('products as p', 'p.id', '=', 'rp.product_id')
                ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                ->where('c.name', 'open_product')->where('pc.status_id', 1)->whereIn('pc.product_id', $productIds)->orderBy('pc.product_id')->get();

            // return $openProductRentals;
            $repeatBookingWithOpenProduct = $openProductRentals->count() > 0 ? true : false;

            $meta = $request->input('repeats');
            $facilityBookingRepeat = new FacilityBookingRepeat;
            $facilityBookingRepeat->meta = $meta;
            $facilityBookingRepeat->repeat_count = $count;
            $facilityBookingRepeat->save();
            $repeatBookingId = $facilityBookingRepeat->id;

            foreach ($request->input('repeats') as $key => $repeat) {
                $repeat = (object)$repeat;
                $overLapping = false;
                $currentDuration = Carbon::parse($repeat->start_time)->diffInMinutes(Carbon::parse($repeat->end_time));
                if ($currentDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $currentDuration += 1;
                }

                $currentProductIds = [];
                $currentProducts = isset($repeat->products) ? $repeat->products : [];
                foreach ($currentProducts as $key => $currentProduct) {
                    $currentProduct = json_decode($currentProduct);
                    $currentProductIds[] = $currentProduct->id;

                    if (($key = array_search($currentProduct->id, $rentalProductIds)) !== false) {
                        unset($rentalProductIds[$key]);
                    }
                }
                // log::info($currentProductIds);
                if ($repeatBookingWithOpenProduct) {
                    $rentalProducts = $openProductRentals;
                } else {
                    $rentalProducts = DB::table('rental_products as rp')
                        ->select(
                            'oi.id',
                            'fr.weekdays',
                            'fr.start_time',
                            'fr.end_time',
                            'rp.product_id',
                            'rp.duration',
                            DB::raw("($currentDuration/rp.duration) as quantity"),
                            'p.price',
                            'p.tax_amount',
                            'p.total_price'
                        )
                        ->join('facility_rental_products as frp', 'frp.rental_product_id', '=', 'rp.id')
                        ->join('facility_rentals as fr', 'frp.facility_rental_id', '=', 'fr.id')
                        ->join('products as p', 'p.id', '=', 'rp.product_id')
                        ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                        ->whereIn('rp.product_id', $currentProductIds)->where('fr.facility_id', $facilityId)
                        ->where(function ($query) use ($repeat) {
                            $query->where(function ($query) use ($repeat) {
                                $query->where('fr.start_time', '<=', $repeat->start_time);
                                $query->where('fr.end_time', '>=', $repeat->end_time);
                            });
                        })->where(['fr.status_id' => 1, 'rp.status_id' => 1, 'frp.status_id' => 1, 'oi.order_id' => $order->id])
                        ->groupBy('rp.product_id')->orderBy('fr.start_time', 'asc')
                        ->orderBy('rp.duration', 'desc')->get();
                    if ($rentalProducts->count() == 0) {
                        $overLapping = false;
                        $rentalProducts = DB::table('rental_products as rp')
                            ->select(
                                'oi.id',
                                'fr.weekdays',
                                'fr.start_time',
                                'fr.end_time',
                                'rp.product_id',
                                'rp.duration',
                                DB::raw("round(
                                           if('$endTime' > fr.end_time,
                                        ((TIME_TO_SEC(fr.end_time) - TIME_TO_SEC(if('$startTime' < fr.start_time,fr.start_time,'$startTime')))/60)/rp.duration,
                                        ((TIME_TO_SEC('$endTime') - TIME_TO_SEC(fr.start_time))/60)/rp.duration
                                    ),
                                    2
                                    ) as quantity"),
                                'p.price',
                                'p.tax_amount',
                                'p.total_price'
                            )
                            ->join('facility_rental_products as frp', 'frp.rental_product_id', '=', 'rp.id')
                            ->join('facility_rentals as fr', 'frp.facility_rental_id', '=', 'fr.id')
                            ->join('products as p', 'p.id', '=', 'rp.product_id')
                            ->join('order_items as oi', 'oi.product_id', '=', 'rp.product_id')
                            ->whereIn('rp.product_id', $currentProductIds)->where('fr.facility_id', $facilityId)
                            ->where(function ($query) use ($repeat) {
                                $query->orWhere(function ($query) use ($repeat) {
                                    $query->where(function ($query) use ($repeat) {
                                        $query->whereRaw("fr.start_time < '$repeat->start_time' and fr.end_time > '$repeat->start_time'");
                                    })->orWhere(function ($query) use ($repeat) {
                                        $query->whereRaw("fr.start_time < '$repeat->end_time' and fr.end_time > '$repeat->end_time'");
                                    })->orWhere(function ($query) use ($repeat) {
                                        $query->orWhereRaw("fr.start_time between '$repeat->start_time' and '$repeat->end_time'");
                                    })->orWhere(function ($query) use ($repeat) {
                                        $query->orWhereRaw("fr.end_time between '$repeat->start_time' and '$repeat->end_time'");
                                    });
                                });
                            })->where(['fr.status_id' => 1, 'rp.status_id' => 1, 'frp.status_id' => 1, 'oi.order_id' => $order->id])
                            ->groupBy('rp.product_id')->orderBy('fr.start_time', 'asc')
                            ->orderBy('fr.start_time', 'desc')->orderBy('rp.duration', 'desc')->get();
                    }
                }
                $weekdays = Weekday::all();
                $repeatData = [];
                $repeatDatesRow = [];
                // log::info($rentalProducts);
                $countOfDays = count($repeat->selected_dates);
                foreach ($repeat->selected_dates as $date) {
                    $repeatData[$date] = ['product_id'];
                    $dayOfWeek = Carbon::parse($date)->englishDayOfWeek;
                    $bitValue = $weekdays->firstWhere('name', $dayOfWeek)->bit_value;
                    $value = (array)['date' => $date, 'start_time' => $repeat->start_time, 'end_time' => $repeat->end_time];
                    $repeatDatesRow[] = $value;
                    $request->merge($value);
                    $facilityBooking = $this->createFacilityBooking($request, $order, $repeatBookingId);
                    foreach ($rentalProducts as $rental) {
                        $isCurrentProductExist = false;
                        foreach ($currentProducts as $key => $currentProduct) {
                            $currentProduct = json_decode($currentProduct);
                            if ($currentProduct->id == $rental->product_id) {
                                $isCurrentProductExist = true;
                                $currentProductQnt = $currentProduct->quantity;
                            }
                        }
                        if ($isCurrentProductExist) {
                            $currentProductQnt = $currentProductQnt / $countOfDays;
                        } else {
                            $currentProductQnt = $rental->quantity;
                        }
                        // log::info($currentProductQnt);
                        if ($repeatBookingWithOpenProduct || ($rental->weekdays && $bitValue)) {
                            $facilityBookingItem = FacilityBookingItem::updateOrCreate(['order_item_id' => $rental->id, 'facility_booking_id' => $facilityBooking->id]);
                            $facilityBookingItem->quantity = $currentProductQnt;
                            if ($rental->price > 0) {
                                $facilityBookingItem->price = (float)$rental->price * $currentProductQnt;
                            }
                            if ($rental->total_price > 0) {
                                $facilityBookingItem->total = (float)$rental->total_price * $currentProductQnt;
                            }
                            if ($rental->tax_amount > 0) {
                                $facilityBookingItem->tax = (float)$rental->tax_amount * $currentProductQnt;
                            }
                            // log::info($facilityBookingItem);
                            $facilityBookingItem->save();
                        }
                    }
                }
            }
            // log::info(count($rentalProductIds));
            if (count($rentalProductIds)) {
                $allRepeatDates = [];
                foreach ($request->input('repeats') as $repeatDate) {
                    $repeatDate = (object)$repeatDate;
                    foreach ($repeatDate->selected_dates as $date) {
                        $allRepeatDates[] = (object)[
                            'date' => $date,
                            'start_time' => $repeatDate->start_time
                        ];
                    }
                }
                $countOfDays = count($allRepeatDates);
                foreach ($allRepeatDates as $date) {
                    $facilityBooking = FacilityBooking::where(['order_id' => $order->id, 'facility_booking_repeat_id' => $repeatBookingId, 'date' => $date->date, 'start_time' => $date->start_time])->first();
                    $orderItems = OrderItem::select('id', 'product_id', 'quantity', 'price', 'tax', 'total')
                        ->whereIn('product_id', $rentalProductIds)->where('order_id', $order->id)->get();
                    foreach ($orderItems as $item) {
                        $facilityBookingItem = FacilityBookingItem::updateOrCreate(['order_item_id' => $item->id, 'facility_booking_id' => $facilityBooking->id]);
                        $facilityBookingItem->quantity = $item->quantity / $countOfDays;
                        if ($item->price > 0) {
                            $facilityBookingItem->price = (float)$item->price / $countOfDays;
                        }
                        if ($item->total > 0) {
                            $facilityBookingItem->total = (float)$item->total / $countOfDays;
                        }
                        if ($item->tax > 0) {
                            $facilityBookingItem->tax = (float)$item->tax / $countOfDays;
                        }
                        $facilityBookingItem->save();
                    }
                }
            }
        }
        else if ($request->has('repeats') && $request->has('repeat_id')) {
            $facilityBooking = FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->first();

            if ($request->has('id_proof_type_id')) {
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof_type_id' => $request->input('id_proof_type_id')]);
            }
            if ($request->has('id_proof_number')) {
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof_number' => $request->input('id_proof_number')]);
            }
            if ($request->hasFile('id_proof')) {
                $file = $this->fileUpload($request->file('id_proof'), 'facility/booking/id', null);
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['id_proof' => $file->file_path]);
            }
            if ($request->hasFile('profile_image')) {
                $file = $this->fileUpload($request->file('profile_image'), 'facility/booking/image', null);
                FacilityBooking::where('facility_booking_repeat_id', $request->input('repeat_id'))->update(['image_path' => $file->file_path]);
            }
        }
        else if (!$request->has('repeats')) {
            $facilityBooking = $this->createFacilityBooking($request, $order, $repeatBookingId, $fullDay);
            if ($fac) {
                if ($fac->is_enable_per_day_capacity == 0) {
                    $total_attendance = FacilityBooking::where('facility_id', $facilityId)->where('start_time', $request->input('start_time'))->where('end_time', $request->input('end_time'))->where('date', $request->date)->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7])->sum('attendance');

                    $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)->where('start_time', $request->input('start_time'))->where('end_time', $request->input('end_time'))->where('date', $request->date)->first();
                } else {
                    $total_attendance = FacilityBooking::where('facility_id', $facilityId)->where('date', $request->date)->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7])->sum('attendance');
                    $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)->where('date', $request->date)->first();
                }
                if ($fac && $fac->capacity && $total_attendance > $fac->capacity) {
                    throw new \Exception('Trying to book more than available capacity');
                }

                if ($perCapacity) {

                    $thresholdCheck = $this->checkCustomerBookingThreshold($request, $fac, $customer);
                    if (!$thresholdCheck) {
                        DB::rollback();
                        //                        throw new \Exception('Customer Booking Threshold Reached');
                        throw new \Exception('You Are Exceeding Maximum Allowed Tickets');
                    }
                }
                /** send email threshold */
                if (!$facilityThresholdEmailLog && $fac && $fac->is_enable_email_threshold == 1 && $fac->online_threshold > 0 && $fac->email_threshold_recipient != "" && $total_attendance >= $fac->online_threshold) {

                    $facilityThresholdEmailLog = new FacilityThresholdEmailLog();
                    $facilityThresholdEmailLog->facility_id = $facilityId;
                    $facilityThresholdEmailLog->date = $request->date;
                    $facilityThresholdEmailLog->start_time = $request->input('start_time');
                    $facilityThresholdEmailLog->end_time = $request->input('end_time');
                    $facilityThresholdEmailLog->save();
                    $result = MailHelper::sendEmail($fac->email_threshold_recipient, new FacilityBookingThresholdEmail($fac, $facilityThresholdEmailLog, $customer, $this->venue));
                }
                /** end email threshold */

                // facility booking email notification
                if ($fac && $fac->is_enable_email_booking == 1 && $fac->email_booking_recipient != "" && $fac->email_booking_title != "" && $fac->email_booking_message != "") {
                    $bookingDate = $request->date;
                    $bookingTime = $request->input('start_time');
                    $customerName = $customer->first_name . ' ' . $customer->last_name;
                    $result = MailHelper::sendEmail($fac->email_booking_recipient, new FacilityBookingEmail($fac, $bookingDate, $bookingTime, $customerName, $this->venue));
                }
            }
            $orderItems = OrderItem::select('id', 'product_id', 'quantity', 'price', 'tax', 'total')
                ->whereIn('product_id', $rentalProductIds)->where('order_id', $order->id)->get();
            foreach ($orderItems as $item) {
                $facilityBookingItem = FacilityBookingItem::updateOrCreate(['order_item_id' => $item->id, 'facility_booking_id' => $facilityBooking->id]);
                $facilityBookingItem->quantity = $item->quantity;
                if ($item->price > 0) {
                    $facilityBookingItem->price = (float)$item->price;
                }
                if ($item->total > 0) {
                    $facilityBookingItem->total = (float)$item->total;
                }
                if ($item->tax > 0) {
                    $facilityBookingItem->tax = (float)$item->tax;
                }
                $facilityBookingItem->save();
            }
        }
        GroupCustomer::where('order_id', $order->id)->delete();
        if (!$venueServiceConfiguration->is_golf_enabled) {
            if (isset($facilityBooking) && $facilityBooking && $facilityBooking->is_open_dated == 1) {
                $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id, 'facility_booking_id' => $facilityBooking->id]);
            } else {
                $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id]);
                $config = VenueService::find($request->venue_service_id);
                if(isset($facilityBooking) && $config->is_my_lap_enabled){
                    $race = Races::where([
                        'venue_service_id' => $request->venue_service_id,
                        'venue_id' => $this->venueId,
                        'date' => $facilityBooking->date,
                        'start_time' => $facilityBooking->start_time,
                        'end_time' => $facilityBooking->end_time,
                        'facility_id' => $facilityBooking->facility_id,
                    ])->first();

                    if($race){
                        if($race->status_id != 11){
                            throw new \Exception('Cannot add participant to this race');
                        }
                        RaceParticipants::updateOrCreate([
                            'race_id' => $race->id,
                            'order_id' => $order->id,
                            'facility_booking_id' => $facilityBooking->id,
                            'vehicle_id' => null,
                            'transponder_id' => null,
                            'group_customer_id' => $orderGroupCustomer->id,
                            'status_id' => 1
                        ]);
                    }
                }

            }

            if ($request->has('addOnCustomers') && is_array($request->input('addOnCustomers')) && count($request->input('addOnCustomers')) > 0) {
                foreach ($request->input('addOnCustomers') as $addOns) {
                    $data_for_request = (array)$addOns;
                    $myRequest = new Request($data_for_request);
                    $addOns = $this->createCustomer($myRequest);
                    // log::info(json_encode($addOns));
                    // log::info($myRequest);

                    $addOnOrderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'customer_id' => $addOns->id, 'status_id' => 1])->first();
                    if (!empty($addOnOrderGroupCustomer)) {
                        $addOnOrderGroupCustomer->customer_id = $addOns->id;
                        $addOnOrderGroupCustomer->order_id = $order->id;
                    } else {
                        if (isset($facilityBooking) && $facilityBooking && $facilityBooking->is_open_dated == 1) {
                            $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id, 'facility_booking_id' => $facilityBooking->id]);
                        } else {
                            $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id]);
                            $config = VenueService::find($request->venue_service_id);
                            if(isset($facilityBooking) && $config->is_my_lap_enabled){
                                $race = Races::where([
                                    'venue_service_id' => $request->venue_service_id,
                                    'venue_id' => $this->venueId,
                                    'date' => $facilityBooking->date,
                                    'start_time' => $facilityBooking->start_time,
                                    'end_time' => $facilityBooking->end_time,
                                    'facility_id' => $facilityBooking->facility_id,
//                                    'status_id' => 11
                                ])->first();

                                if($race){
                                    if($race->status_id != 11){
                                        throw new \Exception('Cannot add participant to this race');
                                    }
                                    RaceParticipants::updateOrCreate([
                                        'race_id' => $race->id,
                                        'order_id' => $order->id,
                                        'facility_booking_id' => $facilityBooking->id,
                                        'vehicle_id' => null,
                                        'transponder_id' => null,
                                        'group_customer_id' => $addOnOrderGroupCustomer->id,
                                        'status_id' => 1
                                    ]);
                                }
                            }
                        }
                    }
                    $addOnOrderGroupCustomer->save();
                }
            }
        } else {
            $orderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'status_id' => 1])->first();
            if (!empty($orderGroupCustomer)) {
                $orderGroupCustomer->customer_id = $customer->id;
                $orderGroupCustomer->order_id = $order->id;
            } else {
                $orderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $customer->id, 'order_id' => $order->id]);
            }

            if ($request->has('addOnCustomers') && is_array($request->input('addOnCustomers')) && count($request->input('addOnCustomers')) > 0) {
                foreach ($request->input('addOnCustomers') as $addOns) {
                    $data_for_request = (array)$addOns;
                    $myRequest = new Request($data_for_request);
                    $addOns = $this->createCustomer($myRequest);
                    // log::info($myRequest);
                    // log::info(json_encode($addOns));
                    $adds[] = $addOns->id;
                    $addOnOrderGroupCustomer = GroupCustomer::where(['order_id' => $order->id, 'customer_id' => $addOns->id, 'status_id' => 1])->first();
                    if (!empty($addOnOrderGroupCustomer)) {
                        $addOnOrderGroupCustomer->customer_id = $addOns->id;
                        $addOnOrderGroupCustomer->order_id = $order->id;
                    } else {
                        if (isset($facilityBooking) && $facilityBooking && $facilityBooking->is_open_dated == 1) {
                            $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id, 'facility_booking_id' => $facilityBooking->id]);
                        } else {
                            $addOnOrderGroupCustomer = GroupCustomer::updateOrCreate(['customer_id' => $addOns->id, 'order_id' => $order->id]);
                        }
                    }
                    $facilityBookingId = isset($facilityBooking) && $facilityBooking->id ? $facilityBooking->id : NULL;
                    if ($venueServiceConfiguration->is_golf_enabled) {
                        $addOnOrderGroupCustomer->facility_booking_id = $facilityBookingId;
                    }
                    $addOnOrderGroupCustomer->save();
                }
            }
        }
        /** save facility_booking_id for golf group customers */
        $facilityBookingId = isset($facilityBooking) && $facilityBooking->id ? $facilityBooking->id : NULL;
        if ($venueServiceConfiguration->is_golf_enabled) {
            $orderGroupCustomer->facility_booking_id = $facilityBookingId;
        }
        $orderGroupCustomer->save();

        if ($facilityBooking->facility_booking_duplicate_id == null && $perCapacity == 1) {
            /** save group customer for golf booking in duplicate booking */
            $customer_id = null;
            if ($venueServiceConfiguration->is_golf_enabled) {
                $customer_id = $customer->id;
            }
            FacilityBookingHelper::saveDuplicateBooking($productIds, $request, $facilityBooking, $customer_id, $adds);
        }


        if ($facilityBooking->customer_type == 'corporate' && $request->has('company_sale_id')) {
            CompanySaleOrder::updateOrCreate(['company_sale_id' => $request->input('company_sale_id'), 'order_id' => $order->id]);
            $orderPayment = new OrderPayment;
            $orderPayment->order_id = $order->id;
            $orderPayment->payment_method_id = 5;
            $orderPayment->total = $order->total;
            $tax = $order->tax;
            $orderPayment->tax = $tax;
            $orderPayment->amount = $order->total - $tax;
            $orderPayment->status_id = 1; // paid
            $orderPayment->save();
            $creditOrder = new CreditOrder;
            $creditOrder->order_payment_id = $orderPayment->id;
            $creditOrder->order_id = $order->id;
            $creditOrder->credit = $order->total;
            $order->order_seq_no = $this->generateSequence('R', $order->id);
            $creditOrder->status_id = 1;
            $creditOrder->save();
            $order->status_id = 4;
            $facilityBooking->status_id = 1;
            $order->paid_at = Carbon::now('UTC')->format('Y-m-d H:i:s');
            $order->paid_by = $this->userId;
            $order->save();
            $facilityBooking->save();
        }
        // $request->request->remove('customer_id');
        // $request->request->remove('first_name');
        // $request->request->remove('last_name');

        // if (is_array($request->input('attendance_customer')) && count($request->input('attendance_customer')) > 0) {
        //     foreach ($request->input('attendance_customer') as $customers) {
        //         $customer = (object) $customers;
        //         $request->merge(['name' => $customer->name]);
        //         $request->merge(['mobile' => $customer->mobile]);
        //         $request->merge(['email' => $customer->email]);
        //         $customerData = $this->createCustomer($request);
        //         $groupCustomer = new GroupCustomer;
        //         $groupCustomer->customer_id = $customerData->id;
        //         $groupCustomer->order_id = $order->id;
        //         $groupCustomer->save();
        //     }
        // }
        if (!$request->has('order_id') && $this->venue->enable_email) {
            if ($request->has('is_kiosk')) {
                if ($order->total > 0) {
                    OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                }
            } else {
                if ($this->venue->salesConfiguration && isset($this->venue->salesConfiguration->enable_email)) {
                    if ($this->venue->salesConfiguration->enable_email == 1) {
                        OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                    }
                } else {
                    OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
                }
                //                OrderHelper::sendOrderConfirmationMail($order->id, $this->venue);
            }
        }
        if ($order->invoice_generated == 1) {
            InvoiceHelper::updateInvoiceUsingOrder($order->id);
        }
        return $facilityBooking;
    }

    /**
     * Update an facility booking
     *
     * @param Request $request
     * @param Integer $id
     * @return Response
     */
    public function update(Request $request, $id)
    {
        $request->request->add(['id' => $id]);
        return $this->addOrEditBooking($request);
    }

    /**
     * validate product change
     *
     * @param object $request
     * @return object
     */
    protected function validateProductChange($request)
    {
        $currentOrderItems = OrderItem::where(['order_id' => $request->input('order_id'), 'status_id' => 5])->get();
        $currentOrderItems = collect($currentOrderItems);
        $newProducts = collect($request->input('products'));
        $productChange = false;

        foreach ($currentOrderItems as $key => $item) {
            $item = (object)$item;
            $isProduct = $newProducts->firstWhere('product_id', '=', $item->product_id);
            if (isset($isProduct)) {
                $isProduct = (object)$isProduct;
                if (floatval($isProduct->quantity) != floatval($item->quantity)) {
                    $productChange = true;
                }
            } else {
                $productChange = true;
            }
        }

        foreach ($newProducts as $key => $item) {
            $item = (object)$item;
            $isProduct = $currentOrderItems->firstWhere('product_id', '=', $item->product_id);
            if (isset($isProduct)) {
                $isProduct = (object)$isProduct;
                if (floatval($isProduct->quantity) != floatval($item->quantity)) {
                    $productChange = true;
                }
            } else {
                $productChange = true;
            }
        }

        if ($productChange) {
            $currentOrder = Order::find($request->input('order_id'));
            $newOrder = OrderHelper::duplicateOrder($currentOrder, $this->generateSequence('O'));
            Order::where('id', $request->input('order_id'))->update(['order_status_id' => 13]);
            $request->merge([
                'order_id' => $newOrder->id,
            ]);
            if ($currentOrder->invoice_generated == 1) {
                InvoiceHelper::updateInvoiceUsingOrder($currentOrder->id, [$newOrder->id]);
            }
        }
    }

    /**
     * Create or update customer on voucher sales
     *
     * @param object $request
     * @return object
     */
    protected function createCustomer($request)
    {
        if($this->venue && $this->venue->enable_icp){
            $customer = $this->createOrUpdateCustomerICP($request);
        }else{
            $customer = $this->createOrUpdateCustomer($request);
        }

        $this->createVenueCustomer($customer, 'Walk-in');
        $this->createVenueCustomerTag($request, $customer->id);
        if($this->venue->save_pii_data){
            $this->createCustomerDocument($request, $customer->id);
        }
        return $customer;
    }

    /**
     * Process sales order
     *
     * @param object $product
     * @param integer $initialBalance
     * @param integer $orderId
     * @return Object
     */
    protected function createOrder($request, $customerId, $discount, $invoice = null)
    {
        $totalPrice = 0;
        $totalTax = 0;
        $totalAmount = 0;

        if ($request->has('order_id')) {
            $orderId = $request->input('order_id');
        } else {
            $orderId = NULL;
        }
        $order_datetime = $request->date . " 00:00:00";
        if ($request->has('start_time') && $request->date) {
            $order_datetime = date("Y-m-d H:i", strtotime($request->date . ' ' . $request->start_time));
            // $order_datetime = Carbon::createFromFormat('Y-m-d H:i', $utcDateTime, $this->venue->timezone)->setTimezone('UTC');
        }

        $oldOrder = Order::find($orderId);

        $order = OrderHelper::createOrUpdateOrder([
            'order_date' => $request->input('date'),
            'order_datetime' => $order_datetime,
            'order_notes' => $request->has('order_notes') ? $request->input('order_notes') : null,
            'invoice_seq_no' => $this->generateSequence('O'),
            'status_id' => 5,
            'order_status_id' => 11,
            'venue_id' => $this->venueId,
            'customer_id' => $customerId,
            'initial_order_id' => $request->initial_order_id,
            'invoice_generated' => $invoice ? 1 : ($oldOrder ? $oldOrder->invoice_generated : 0)
        ], $orderId);

        if ($request->has('products')) {
            $productIds = array_column($request->input('products'), 'product_id');
            $products = collect($request->input('products'));
            $openCount = $products->where('product_id', 0)->where('rental', 'true')->count();

            $newProducts = $products->toArray();
            // throw new \Exception($duration);

            if ($request->has('repeats')) {
                $duration = 0;
                foreach ($request->input('repeats') as $key => $repeat) {
                    $repeat = (object)$repeat;
                    $count = count($repeat->selected_dates);

                    $currentDuration = Carbon::parse($repeat->start_time)->diffInMinutes(Carbon::parse($repeat->end_time));
                    if ($currentDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $currentDuration += 1;
                    }
                    $duration += ($currentDuration * $count);
                }
            } else {
                $startTime = $request->input('start_time');
                $endTime = $request->input('end_time');
                $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
            }
            $date = $request->input('date');
            foreach ($request->input('products') as $key => $item) {

                $item = (object)$item;
                if (isset($item->id)) {
                    $product = Product::with(['productCategory'])->leftJoin('product_seasonal_pricing as psp', function ($join) use ($date) {
                        $join->on('psp.product_id', '=', 'products.id')->when($date, function ($query) use ($date) {
                            $query->where('psp.start_date', '<=', $date)->where('psp.end_date', '>=', $date)->where('psp.status_id', 1);
                        }, function ($query) {
                            $query->where('psp.status_id', 12);
                        });
                    })->where('products.id', $item->id)
                        ->select(
                            'products.*',
                            DB::raw('CASE WHEN psp.tax IS NOT NULL THEN psp.tax ELSE products.tax_amount END AS tax_amount'),
                            DB::raw('CASE WHEN psp.price IS NOT NULL THEN psp.price ELSE products.price END AS price'),
                        )->first();
                } else {
                    $product = Product::with(['productCategory'])->leftJoin('product_seasonal_pricing as psp', function ($join) use ($date) {
                        $join->on('psp.product_id', '=', 'products.id')->when($date, function ($query) use ($date) {
                            $query->where('psp.start_date', '<=', $date)->where('psp.end_date', '>=', $date)->where('psp.status_id', 1);
                        }, function ($query) {
                            $query->where('psp.status_id', 12);
                        });
                    })->where('products.id', $item->product_id)
                        ->select(
                            'products.*',
                            DB::raw('CASE WHEN psp.tax IS NOT NULL THEN psp.tax ELSE products.tax_amount END AS tax_amount'),
                            DB::raw('CASE WHEN psp.price IS NOT NULL THEN psp.price ELSE products.price END AS price'),
                        )->first();
                }

                if (!isset($product)) {
                    $product = $this->saveOpenProduct($item);
                    $newProducts[$key]['product_id'] = $product->id;
                    $newProducts[$key]['category_id'] = $product->category_id;
                    $newProducts[$key]['product_type_id'] = 6;

                    if ($item->rental == 'true') {
                        $rentalDuration = (($duration / $openCount) / $item->quantity);
                        $rentalProduct = new RentalProduct;
                        $rentalProduct->product_id = $product->id;
                        $rentalProduct->duration = $rentalDuration;
                        $rentalProduct->status_id = 14;
                        $rentalProduct->venue_service_id = $request->input('venue_service_id');
                        $rentalProduct->save();
                    }

                    $productIds[] = $product->id;
                    $productCategory = ProductCategory::updateOrCreate(['product_id' => $product->id, 'category_id' => $product->category_id]);
                }

                if ($request->has('order_id')) {
                    $orderItem = OrderItem::where(['product_id' => $product->id, 'order_id' => $request->input('order_id')])->whereNotIn('status_id', [1]);

                    if ($orderItem->exists()) {
                        $orderItem = $orderItem->first();
                    } else {
                        $orderItem = NULL;
                    }
                } else {
                    $orderItem = NULL;
                }

                $quantity = $item->quantity;
                $price = $product->price * $quantity;
                $tax = $product->tax_amount * $quantity;
                $total = $price + $tax;

                $totalPrice += $price;
                $totalTax += $tax;
                $totalAmount += $total;


                $orderItem = OrderHelper::createOrUpdateOrderItem([
                    'order_id' => $order->id,
                    'price' => $price,
                    'tax' => $tax,
                    'product_id' => $product->id,
                    'quantity' => $quantity,
                    'status_id' => 5,
                    'total' => $total,
                    'venue_service_id' => $request->input('venue_service_id')
                ], $orderItem != NULL ? $orderItem->id : NULL);

                if ($discount != null) {
                    $orderItem->category_id = $product->productCategory->category_id;
                    DiscountHelper::applyProductDiscount($discount->benefits, $orderItem);
                    unset($orderItem->category_id);
                }

                FacilityHelper::updateProductInventory($product, $quantity, $request->input('date'), $orderItem);
            }

            OrderItem::whereNotIn('product_id', $productIds)->where('order_id', $request->input('order_id'))
                ->update(['status_id' => 2]);
        }
        if ($request->has('is_kiosk')) {
            $order->source = "Kiosk";
        }
        $order->price = $totalPrice;
        $order->tax = $totalTax;
        $order->total = $totalAmount;
        if ($request->has('sales_team_id')) {
            $order->sales_team_id = $request->input('sales_team_id');
        }
        $order->save();
        if ($request->has('sales_team_id')) {
            $productType = ProductType::where(['name' => 'Facility', 'status_id' => 1])->first();
            if ($productType) {
                $salesTeamCommission = SalesTeamCommission::where(['product_type_id' => $productType->id, 'sales_team_id' => $request->input('sales_team_id'), 'status_id' => 1])->first();
                // Log::info($salesTeamCommission);
                if ($salesTeamCommission) {
                    $commission = ($order->price * $salesTeamCommission->commission_percentage) / 100;
                    $salesTeamOrderCommission = SalesTeamOrderCommission::where('order_id', $order->id)->first();
                    if (!$salesTeamOrderCommission)
                        $salesTeamOrderCommission = new SalesTeamOrderCommission;
                    $salesTeamOrderCommission->sales_team_id = $request->input('sales_team_id');
                    $salesTeamOrderCommission->order_id = $order->id;
                    $salesTeamOrderCommission->total_commission = $commission;
                    $salesTeamOrderCommission->commission_percentage = $salesTeamCommission->commission_percentage;
                    $salesTeamOrderCommission->status_id = 2;
                    $salesTeamOrderCommission->save();
                }
            }
        }
        if ($discount != null) {
            DiscountHelper::applyOrderDiscount($order->id, $discount->type, $discount->id);
        } else if ($discount == null) {
            DiscountHelper::removeDiscount($order->id);
        }


        $request->merge(['products' => $newProducts]);
        // dd(1);
        return $order;
    }

    public function getFacilityEndTime($facility_id, $date)
    {
        $carbonDate = Carbon::parse($date);

        $weekday = $carbonDate->format('l');

        $bitValue = Weekday::where('name', $weekday)->first();
        if (!$bitValue) {
            throw new \Exception('Weekday calculation is wrong');
        }
        $bitValue = $bitValue->bit_value;
        $facility = Facility::whereHas('facilityRentals', function ($q) use ($bitValue) {
            $q->whereRaw("($bitValue & facility_rentals.weekdays) > 0");
        })->with(['facilityRentals' => function ($q) use ($bitValue) {
            $q->whereRaw("($bitValue & facility_rentals.weekdays) > 0");
        }])->find($facility_id);

        if (!$facility) {
            throw new \Exception('Facility not available');
        }


        return $facility->facilityRentals->first()->end_time;
    }

    private function hasOpenDatedTicket($order_id)
    {
        return DB::table('orders', 'o')
            ->join('order_items as oi', 'oi.order_id', '=', 'o.id')
            ->join('products as p', 'p.id', '=', 'oi.product_id')
            ->where('o.id', '=', $order_id)
            ->select(DB::raw('max(p.is_open_dated) as is_open_dated'))
            ->first();
    }

    /**
     * Process facility booking
     *
     * @param object $product
     * @param integer $initialBalance
     * @param integer $orderId
     * @return Object
     */
    protected function createFacilityBooking($request, $order, $repeatId, $fullDay = false)
    {
        $isOpenDated = $this->hasOpenDatedTicket($order->id);
        if ($request->has('id') && !$request->has('repeat_id')) {
            $facilityBooking = FacilityBooking::where(['id' => $request->input('id')])->first();
        } else if ($request->has('id') && $request->has('repeat_id')) {
            $date = $request->input('date');
            $orderId = $request->input('order_id');
            $facilityBooking = FacilityBooking::where(['order_id' => $orderId, 'facility_booking_repeat_id' => $repeatId, 'date' => $date])->whereNotIn('status_id', [2]);
            if ($facilityBooking->exists()) {
                $facilityBooking = $facilityBooking->first();
            } else {
                $facilityBooking = new FacilityBooking;
            }
        } else {
            $facilityBooking = new FacilityBooking;
        }
        if ($facilityBooking == '' || empty($facilityBooking)) {
            $facilityBooking = new FacilityBooking;
        }

        $facilityBooking->date = $request->input('date');
        $facilityBooking->start_time = $request->input('start_time');

        if ($fullDay) {
            $facilityBooking->end_time = $this->getFacilityEndTime($request->input('facility_id'), $request->input('date'));
            $facilityBooking->booking_end_date = FacilityBookingHelper::getFacilityBookingEndDate($request->start_time, $facilityBooking->end_time, $request->date);
        } else if ($request->has('end_time')) {
            $facilityBooking->end_time = $request->input('end_time');
            $facilityBooking->booking_end_date = FacilityBookingHelper::getFacilityBookingEndDate($request->start_time, $request->end_time, $request->date);
        }
        $facilityBooking->facility_id = $request->input('facility_id');
        if ($request->has('facility_id')) {
            $facility = Facility::find($request->facility_id);
            if ($facility && $facility->is_enable_booking_approval && !$request->boolean('is_kiosk')) {
                $facilityBooking->is_booking_approved = 0;
            }
        }

        if ($request->has('customer_type')) {
            $facilityBooking->customer_type = $request->input('customer_type');
        }
        if ($request->has('attendance_count')) {
            $facilityBooking->attendance = $request->input('attendance_count');
        }
        if($this->venue->save_pii_data){
            if ($request->has('id_proof_type_id')) {
                $facilityBooking->id_proof_type_id = $request->input('id_proof_type_id');
            }
            if ($request->has('id_proof_number')) {
                $facilityBooking->id_proof_number = $request->input('id_proof_number');
            }
            if ($request->hasFile('id_proof')) {
                $file = $this->fileUpload($request->file('id_proof'), 'facility/booking/id', null);
                $facilityBooking->id_proof = $file->file_path;
            } else if ($request->has('id_proof_path') && $request->id_proof_path != "") {
                $facilityBooking->id_proof = $request->id_proof_path;
            }
        }
        if ($request->has('duration'))
            $facilityBooking->duration = $request->input('duration');

        if($this->venue->save_pii_data){
            if ($request->hasFile('profile_image')) {
                $file = $this->fileUpload($request->file('profile_image'), 'facility/booking/image', null);
                $facilityBooking->image_path = $file->file_path;
            }
        }
        $facilityBooking->status_id = $order->status_id; // Unpaid
        $facilityBooking->order_id = $order->id;
        $facilityBooking->facility_booking_repeat_id = $repeatId;

        if ($isOpenDated->is_open_dated) {
            $facilityBooking->is_open_dated = 1;
        }

        if ($facilityBooking->customer_type == 'corporate' && $request->has('company_sale_id')) {
            $facilityBooking->status_id = 1;
        }



        $facilityBooking->save();

        // Save disclaimers after facility booking is saved
        $mainCustomer = Customer::find($order->customer_id);
        $venueServiceId = $request->input('venue_service_id');
        $fac = Facility::find($request->input('facility_id'));

        $facilityBookingSvc = new FacilityBookingService();
        $facilityBookingSvc->saveDisclaimers($mainCustomer, $venueServiceId, $this->venueId, $facilityBooking, $fac);

        $this->makeRace($request,$facilityBooking);

        return $facilityBooking;
    }

    public function makeRace($request,$facilityBooking)
    {
        $config = VenueService::find($request->venue_service_id);
        if($config && $config->is_my_lap_enabled){
            $total_laps = 6;
            $laps = VenueServiceConfiguration::where('venue_service_id', $request->venue_service_id)->first();
            if($laps){
                $total_laps = $laps->laps;
            }
            $checkRace = Races::where([
                'venue_service_id' => $config->id,
                'venue_id' => $this->venueId,
                'date' => $facilityBooking->date,
                'start_time' => $facilityBooking->start_time,
                'end_time' => $facilityBooking->end_time,
                'facility_id' => $facilityBooking->facility_id,
            ])->first();

            if(!$checkRace){
                Races::updateOrCreate([
                    'venue_service_id' => $config->id,
                    'venue_id' => $this->venueId,
                    'total_laps' => $total_laps,
                    'date' => $facilityBooking->date,
                    'start_time' => $facilityBooking->start_time,
                    'end_time' => $facilityBooking->end_time,
                    'facility_id' => $facilityBooking->facility_id,
                    'status_id' => 11
                ]);
            }
        }
    }

    public function checkCustomerBookingThreshold($request, $fac, $customer)
    {
        $threshold = CustomerConfigurations::where('venue_id', $this->venueId)->where('status_id', 1)->first();
        if ($threshold && $threshold->customer_threshold != null) {
            if ($fac->is_enable_per_day_capacity == 0) {
                $data = DB::table('orders as o')
                    ->join('facility_bookings as fb', 'fb.order_id', '=', 'o.id')
                    ->where('o.customer_id', $customer->id)
                    ->where('o.venue_id', $this->venueId)
                    ->where('fb.start_time', $request->input('start_time'))->where('fb.end_time', $request->input('end_time'))
                    ->whereIn('fb.status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                    ->where('fb.date', $request->date)
                    ->where('fb.facility_id', $fac->id)
                    ->groupBy('fb.date')
                    ->select(
                        DB::raw('SUM(fb.attendance) as bookings'),
                        'o.id as order_id',
                    )
                    ->get();
            } else {
                $data = DB::table('orders as o')
                    ->join('facility_bookings as fb', 'fb.order_id', '=', 'o.id')
                    ->where('o.customer_id', $customer->id)
                    ->whereIn('fb.status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                    ->where('o.venue_id', $this->venueId)
                    ->where('fb.date', $request->date)
                    ->where('fb.facility_id', $fac->id)
                    ->groupBy('fb.date')
                    ->select(
                        DB::raw('SUM(fb.attendance) as bookings'),
                        'o.id as order_id',
                    )
                    ->get();
            }
            if ($data->count() > 0 && ($data->sum('bookings') > $threshold->customer_threshold)) {
                return false;
            }
        }
        return true;
    }

    public function complementaryPaid($order_id)
    {

        $arr = [
            "isComplementary" => true,
            "order_id" => $order_id,
            "payments" => [
                [
                    "amount" => null,
                    "card_type_id" => null,
                    "payment_code" => null,
                    "payment_method" => "Complementary",
                    "payment_method_id" => 4
                ]
            ]
        ];
        $newRequest = new Request($arr);
        $response = app('App\Http\Controllers\Orders\OrderController')->pay($newRequest);
        return $response;
    }
    public function kioskPaid($order_id)
    {
        $paymentMethod = PaymentMethod::where('name','kiosk')->where('type','online')->where('status_id',1)->first();
        $arr = [
            "isComplementary" => false,
            "order_id" => $order_id,
            "payments" => [
                [
                    "amount" => 0,
                    "card_type_id" => null,
                    "payment_code" => null,
                    "payment_method" => "Kiosk Online",
                    "payment_method_id" => $paymentMethod->id
                ]
            ]
        ];
        $newRequest = new Request($arr);
        $response = app('App\Http\Controllers\Orders\OrderController')->pay($newRequest);
        return $response;
    }

    public function kioskInvoicePaid($invoice_id, $customer_id)
    {
        $paymentMethod = PaymentMethod::where('name','kiosk')->where('type','online')->where('status_id',1)->first();

        $arr = [
            "isComplementary" => false,
            "invoice_id" => $invoice_id,
            "customer_id" => $customer_id,
            "payments" => [
                [
                    "amount" => 0,
                    "card_type_id" => null,
                    "payment_code" => null,
                    "payment_method" => "Kiosk Online",
                    "payment_method_id" => $paymentMethod->id
                ]
            ]
        ];
        $newRequest = new Request($arr);
        $response = app('App\Http\Controllers\Invoices\InvoiceController')->pay($newRequest);
        return $response;
    }
    /**
     * Get an facility booking
     *
     * @param Request $request
     * @param Integer $id
     * @return Response
     */
    public function show(Request $request, $id)
    {
        try {
            // DB::enableQueryLog();
            $venueId = $this->venueId;
            $facilityBooking = Order::select('*')->with(['salesTeam','salesTeam.user',
                'invoice', 'invoice.payments' => function ($query) {
                    $query->leftJoin('general_types as gt', 'gt.id', 'invoice_payments.card_type_id');
                    $query->leftJoin('payment_methods', 'payment_methods.id', 'invoice_payments.payment_method_id');
                    $query->select('invoice_payments.*', 'gt.name as card_type', 'payment_methods.name as payment_method_name');
                }, 'invoiceItem', 'companySale', 'payments' => function ($query) {
                    $query->leftJoin('general_types as gt', 'gt.id', 'order_payments.card_type_id');
                    $query->leftJoin('payment_methods', 'payment_methods.id', 'order_payments.payment_method_id');
                    $query->select('order_payments.*', 'gt.name as card_type', 'payment_methods.name as payment_method_name');
                },
                'walletRedemption' => function ($query) {
                    $query->where('customer_products_wallet_redemption.status_id', 1);
                },
                'creditOrder' => function ($query) {
                    $query->where('status_id', 1);
                }, 'customer' => function ($query) use ($venueId) {
                    $query->with('customerDocuments', function ($q2) use ($venueId) {
                        $q2->where('customer_documents.venue_id', $venueId);
                    });
                    $query->with('additionalData', function ($q2) use ($venueId) {
                        $q2->leftJoin('field_configurations', 'customer_additional_data.field_id', '=', 'field_configurations.id')
                            ->where(function ($subQuery) use ($venueId) {
                                $subQuery->where('customer_additional_data.venue_id', $venueId);
                                $subQuery->where('field_configurations.is_visible', 1);
                            })->select(
                                'customer_additional_data.customer_id',
                                'customer_additional_data.value',
                                'customer_additional_data.venue_id as additional_data_venue_id',
                                'field_configurations.id as id',
                                'field_configurations.slug',
                                'field_configurations.type');
                    });
                    $query->with('venueCustomerTags.tag');
                    $query->leftJoin('countries as ct', 'ct.id', 'customers.country_id')
                        ->join('customer_contacts as cc', 'cc.id', '=', 'customers.customer_contacts_id')
                        ->leftJoin('venue_customers as vc', function ($query) {
                            $query->on('vc.customer_id', 'customers.id');
                            $query->on('vc.venue_id', DB::raw($this->venueId));
                        })
                        ->select('customers.*', 'ct.name as country', 'cc.mobile', 'cc.email', 'vc.opt_marketing', 'vc.alert_notes', 'vc.tag_id as customer_tag','vc.flagged');
                }, 'facilityBooking' => function ($query) {
                    $query->join('orders', 'orders.id', '=', 'facility_bookings.order_id')
                        ->join('customers as c', 'c.id', '=', 'orders.customer_id')
                        ->leftJoin('countries as cnt', 'c.country_id', 'cnt.id')
                        ->leftJoin('training_sessions as ts', function ($query) {
                            $query->on('ts.facility_booking_id', 'facility_bookings.id');
                            $query->on('facility_bookings.is_trainer', DB::raw(1));
                        })
                        ->select('facility_bookings.*', 'ts.is_present', DB::raw("CONCAT(c.first_name, if(c.last_name is not null, CONCAT(' ',c.last_name),'') ) as name"))
                        ->orderBy('c.first_name')
                        ->with(['facility', 'repeat', 'documents']); // 'repeat'
                    $query->whereNull('facility_booking_duplicate_id');
                    $query->whereNotIn('facility_bookings.status_id', [2]);
                },  'discount' => function ($query) {
                    $query->with(['promotion' => function ($q) {
                        $q->where('status_id', 1);
                    }, 'member']);
                }, 'group_customers' => function ($query) {
                    $query->join('customers as gc', 'gc.id', 'group_customers.customer_id')
                        ->join('customer_contacts as cc', 'cc.id', '=', 'gc.customer_contacts_id')
                        ->leftJoin('countries as cnt', 'gc.country_id', 'cnt.id')
                        ->select(
                            'group_customers.order_id',
                            'group_customers.customer_id',
                            'group_customers.customer_id as id',
                            'group_customers.id as group_customer_id',
                            'group_customers.facility_booking_id as gc_fb_id',
                            DB::raw("CONCAT(gc.first_name, if(gc.last_name is not null, CONCAT(' ',gc.last_name),'') ) as name"),
                            'gc.first_name as first_name',
                            'gc.last_name as last_name',
                            'cc.mobile',
                            'cc.email',
                            'group_customers.check_in_time as check_in_time',
                            'group_customers.check_out_time as check_out_time',
                            'group_customers.check_in_and_out as check_in_and_out',
                            'cc.email',
                            'gc.gender',
                            'cnt.name as country',
                            'gc.dob',
                            'gc.country_id',
                            'gc.age_group',
                        )->where('group_customers.status_id', 1);
                }, 'group_customers.venueCustomerTags.tag', 'parentOrderIds','group_customers.additionalData'=>function($q){
                    $q->leftJoin('field_configurations', 'customer_additional_data.field_id', '=', 'field_configurations.id')
                        ->where(function ($subQuery)  {
                            $subQuery->where('customer_additional_data.venue_id', $this->venueId);
                            $subQuery->where('field_configurations.is_visible', 1);
                        })->select(
                            'customer_additional_data.customer_id',
                            'customer_additional_data.value',
                            'customer_additional_data.venue_id as additional_data_venue_id',
                            'field_configurations.id as id',
                            'field_configurations.slug',
                            'field_configurations.type');
                }
            ])
                ->where(['orders.id' => $id])->first();

            $orderItems = OrderItem::with(['rental', 'discount'])
                ->join('products', 'products.id', 'order_items.product_id')
                ->join('orders', 'orders.id', 'order_items.order_id')
                ->leftJoin('product_categories', function ($q) {
                    $q->on('products.id', 'product_categories.product_id')->where('product_categories.status_id', 1);
                })
                ->leftJoin('categories', 'categories.id', 'product_categories.category_id')
                ->select(
                    'products.name',
                    'products.price as product_price',
                    'products.inventory_enable as inventory_enable',
                    'products.product_type_id',
                    'products.participant_count',
                    'product_categories.category_id',
                    'categories.name as category_name',
                    'products.image',
                    'orders.order_seq_no',
                    'orders.invoice_seq_no',
                    'order_items.*',
                    'order_items.id as order_item_id'
                )->whereNotIn('order_items.status_id', [2])
                ->whereNotIn('order_items.product_id', [1])

                ->where(function ($query) use ($facilityBooking) {
                    $query->where('order_items.order_id', $facilityBooking->id);

                    $query->orWhereIn('order_items.order_id', function ($query) use ($facilityBooking) {
                        $query->select('oo.id')->from('orders as oo')->where('oo.initial_order_id', $facilityBooking->id)->whereNotIn('order_status_id', [13, 2]);
                        if ($facilityBooking->initial_order_id != null) {
                            // $query->where('oo.initial_order_id', $facilityBooking->initial_order_id);
                            $query->orWhere(function ($query) use ($facilityBooking) {
                                $query->where(function ($q) use ($facilityBooking) {
                                    return $q->whereIn('oo.initial_order_id', [$facilityBooking->id, $facilityBooking->initial_order_id])->orWhereIn('oo.id', [$facilityBooking->id, $facilityBooking->initial_order_id]);
                                })
                                    ->whereNotIn('order_status_id', [13, 2]);
                            });
                        }
                    });
                })->groupBy('order_items.id')->orderBy('orders.id', 'desc');
            //                ->get();
            // $log = Str::replaceArray('?', $orderItems->getBindings(), $orderItems->toSql());
            // Log::info('--------show facility booking details ' . json_encode($log));
            $orderItems =  $orderItems->get();
            if (isset($facilityBooking->discount)) {
                $discount = $facilityBooking->discount;
                $discount->actual_price = 0;
                $discount->actual_tax = 0;
                $discount->actual_total = 0;
                foreach ($orderItems as $orderItem) {
                    if (isset($orderItem->discount)) {
                        $discount->actual_price += (int)$orderItem->discount->actual_price;
                        $discount->actual_tax += (int)$orderItem->discount->actual_tax;
                        $discount->actual_total += (int)$orderItem->discount->actual_total;
                    } else {
                        $discount->actual_price += (int)$orderItem->price;
                        $discount->actual_tax += (int)$orderItem->tax;
                        $discount->actual_total += (int)$orderItem->total;
                    }
                }
                $facilityBooking->discount = $discount;
            }
            $facilityBooking->total = $orderItems->sum('total');
            $facilityBooking->price = $orderItems->sum('price');
            $facilityBooking->tax = $orderItems->sum('tax');

            if ($facilityBooking->initial_order_id) {
                $parent_orders_id = Order::where('initial_order_id', $facilityBooking->initial_order_id)->whereNotIn('status_id', [14])->pluck('id')->toArray();
                $parent_orders_id[] = $facilityBooking->initial_order_id;
                $facilityBooking->parent_orders_id = $parent_orders_id;
            } else {
                $facilityBooking->parent_orders_id = $facilityBooking->parentOrderIds->pluck('id');
                if (sizeof($facilityBooking->parent_orders_id) > 0) {
                    $facilityBooking->parent_orders_id[] = (int)$id;
                }
            }

            $facilityBooking->items = $orderItems;
            if ($facilityBooking->facilityBooking->count() > 0) {
                if (isset($facilityBooking->facilityBooking[0]->repeat)) {

                    if(isset($facilityBooking->initial_order_id)){
                        $parentOrdersIdForRepeat = Order::where(function($q) use ($facilityBooking){
                            $q->where('initial_order_id', $facilityBooking->initial_order_id)
                                ->orWhere('id',$facilityBooking->initial_order_id);
                        })->whereNotIn('status_id', [14, 5, 13])->whereNotIn('order_status_id', [13])->pluck('id')->toArray();
                    }else{
                        $parentOrdersIdForRepeat = Order::where('initial_order_id',$id)->whereNotIn('status_id', [14, 5, 13])->whereNotIn('order_status_id', [13])->pluck('id')->toArray();
                    }

                    if (!isset($facilityBooking->initial_order_id)) {
                        $parentOrdersIdForRepeat[] = (int)$id;
                    }

                    $facilityBooking->repeat_booking_items = OrderItem::with(['rental', 'discount'])
                        ->join('products', 'products.id', 'order_items.product_id')
                        ->join('orders', 'orders.id', 'order_items.order_id')
                        ->leftJoin('product_categories', 'products.id', 'product_categories.product_id')
                        ->leftJoin('categories', 'categories.id', 'product_categories.category_id')
                        ->select(
                            'products.name',
                            'products.price as product_price',
                            'products.product_type_id',
                            'product_categories.category_id',
                            'categories.name as category_name',
                            'products.image',
                            'orders.order_seq_no',
                            'orders.invoice_seq_no',
                            'order_items.*',
                        )->where('order_items.status_id', '!=', 2)
                        ->where('product_categories.status_id', 1)
                        ->whereIn('order_id', $parentOrdersIdForRepeat)
                        ->groupBy('order_items.id')->get();
                }
            }

            $message = $request->has('voucher_sales_id') ? config('constants.update_success') : config('constants.add_success');
            return response()->json(['status' => true, 'message' => $message, 'data' => $facilityBooking], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Get an booking participants
     *
     * @param Request $request
     * @param Integer $id
     * @return Response
     */
    public function getBookingParticipants(Request $request)
    {
        try {

            $rules = [
                'start_time' => 'date_format:H:i:s',
                'end_time' => 'date_format:H:i:s|after:start_time',
                'date' => 'required|date_format:Y-m-d',
                'facility_id' => 'integer|required'
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }
            $startTime = $request->input('start_time');
            $endTime = $request->input('end_time');
            $date = $request->input('date');
            $participants = DB::table('facility_bookings as fb')
                ->select(
                    'fb.id',
                    'fb.attendance',
                    'fb.date',
                    'fb.start_time',
                    'fb.end_time',
                    'fb.order_id',
                    'fb.status_id',
                    'fb.facility_id',
                    'f.name as facility_name',
                    'f.per_capacity',
                    'f.min_booking_time',
                    DB::raw("CONCAT(c.first_name, if(c.last_name is not null, CONCAT(' ',c.last_name),'') ) as name"),
                    'c.profile_image',
                    'c.id as customer_id',
                    'o.id as order_id',
                    'vc.alert_notes as alert_notes',
                    'fb.id as booking_id',
                    'o.total as order_total',
                    'o.invoice_seq_no',
                    'o.order_seq_no',
                    DB::raw("COALESCE(cro.credit,0) as credit_owed"),
                    'vc.flagged'
                )
                ->join('facilities as f', 'f.id', '=', 'fb.facility_id')
                ->join('orders as o', 'o.id', '=', 'fb.order_id')
                ->join('group_customers as gc', function ($join) {
                    $join->on('gc.order_id', '=', 'o.id');
                    $join->on('gc.status_id', '=', DB::raw(1));
                })
                ->join('customers as c', 'c.id', '=', 'gc.customer_id')
                ->join('venue_customers as vc', function ($query) {
                    $query->on('c.id', '=', 'vc.customer_id');
                    $query->where('vc.venue_id', '=', $this->venueId);
                })
                ->leftJoin('credit_orders as cro', function ($query) {
                    $query->on('cro.order_id', 'o.id');
                    $query->where('cro.status_id', '=', 1);
                })
                ->where(function ($query) use ($startTime, $endTime) {
                    $query->where('fb.start_time', '=', $startTime)->orWhere('fb.end_time', '=', $endTime);
                    $query->orWhereRaw("'$startTime' between fb.start_time and fb.end_time and '$endTime' between fb.start_time and fb.end_time");
                })
                ->where('fb.status_id', '!=', 2)
                ->where('fb.facility_id', $request->input('facility_id'))->where('fb.date', '=', $date)
                ->groupBy('gc.id')
                ->get();
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $participants], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Get an facility booking
     *
     * @param Request $request
     * @return Response
     */
    public function getFacilityCalender(Request $request)
    {
        try {

            $rules = [
                'venue_service_ids' => 'required|array',
                'facility_ids' => 'array|nullable',
                'date' => 'required|date_format:Y-m-d',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }

            $date = Carbon::parse($request->input('date'));
            $start = $date->startOfMonth()->format('Y-m-d');
            $end = $date->endOfMonth()->format('Y-m-d');
            $facilityBooking = DB::table('facility_bookings as fb')
                ->leftJoin('facilities as f', 'f.id', 'fb.facility_id')
                ->leftJoin('facility_services as fs', 'fs.facility_id', 'f.id')
                ->leftJoin('facility_booking_items as fbi', 'fbi.facility_booking_id', 'fb.id')
                //                ->leftJoin('order_items as oi', 'fbi.order_item_id', 'oi.id')
                ->leftJoin('orders as o', 'fb.order_id', 'o.id')
                ->leftJoin('statuses as s', 's.id', 'o.status_id')
                //                ->leftJoin('rental_products as rp', 'rp.product_id', 'oi.product_id')
                ->where('fb.date', '<=', $end)
                ->where('fb.date', '>=', $start)
                ->where(['f.venue_id' => $this->venueId])
                ->whereNotIn('fbi.status_id', [2])
                ->whereIn('fb.status_id', [1, 5, 4])
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->whereIn('o.status_id', [4, 8, 5])
                            ->WhereIn('o.order_status_id', [8, 11, 12, 17, 18, 20]);
                    })->orWhere(function ($query) {
                        $query->where('o.status_id', 13)
                            ->Where('o.order_status_id', 13);
                        //                            ->Where('oi.status_id', 13);
                    });
                })
                ->select(
                    'fb.date',
                    's.name as status',
                    'f.per_capacity as capacity_based',
                    DB::raw('SUM(fbi.quantity) as bookings'),
                    DB::raw('SUM(fb.attendance) as participants'),
                    DB::raw('group_concat(DISTINCT fb.order_id) as fb_orders'),
                    DB::raw("SUM(fbi.total) as sales")
                )
                ->where(function ($query) {
                    $query->where(function ($query) {
                        $query->whereIn('o.status_id', [4, 8, 5])
                            ->WhereIn('o.order_status_id', [8, 11, 12, 17, 18, 20]);
                    })->orWhere(function ($query) {
                        $query->where('o.status_id', 13)
                            ->Where('o.order_status_id', 13);
                    });
                })
                //                ->where('oi.status_id', '!=', 2)
                ->where('f.status_id', 1)
                ->whereIn('fs.venue_service_id', $request->input('venue_service_ids'))
                ->groupBy('fb.date', 'o.status_id')->get();

            $data = collect([]);
            $period = CarbonPeriod::create($start, $end);

            // $log = Str::replaceArray('?', $facilityBooking->getBindings(), $facilityBooking->toSql());
            // Log::info('--------api testing log' . json_encode($log));

            foreach ($period as $date) {
                $booking = $facilityBooking->first(function ($value) use ($date) {
                    return ($value->date == $date->format('Y-m-d') && $value->status == 'Paid');
                });

                $reservation = $facilityBooking->first(function ($value) use ($date) {
                    return ($value->date == $date->format('Y-m-d') && $value->status == 'Unpaid');
                });

                $innerData = collect(['bookings' => ['sales' => 0, 'booking' => 0], 'reservations' => ['sales' => 0, 'booking' => 0, 'capacity_based' => 0, 'participants' => 0], 'day_index' => $date->format('d'), 'date' => $date->format('Y-m-d')]);
                if ($booking) {
                    unset($innerData['bookings']);
                    //                    $booking->sales = $this->getSales($booking->fb_orders)[0];
                    $innerData['bookings'] = collect(['sales' => $booking->sales, 'booking' => $booking->bookings, 'participants' => $booking->participants, 'capacity_based' => $booking->capacity_based]);
                }
                if ($reservation) {
                    unset($innerData['reservations']);
                    //                    $reservation->sales = $this->getSales($reservation->fb_orders)[0];
                    $innerData['reservations'] = collect(['sales' => $reservation->sales, 'booking' => $reservation->bookings, 'participants' => $reservation->participants, 'capacity_based' => $reservation->capacity_based]);
                }
                $data->push($innerData);
            }

            return response()->json(['status' => true, 'message' => 'Success', 'data' => $data, 'gest' => $facilityBooking,], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    private function getSales($ids)
    {
        $order = DB::table('orders as o')->select(DB::raw('sum(o.total) as sales'))->whereIn('id', explode(',', $ids));
        return $order ? $order->pluck('sales') : 0;
    }

    /**
     * this function is not use anywhere so keep remove this function later
     *
     * @param Request $request
     * @return Response
     */
    public function getFacilityCalender_old(Request $request)
    {
        try {
            $rules = [
                'facility_ids' => 'array|nullable',
                'date' => 'required|date_format:Y-m-d',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }

            $date = $request->input('date');
            $monthNumber = date('m', strtotime($date));
            $year = date('Y', strtotime($date));
            $day_count = cal_days_in_month(CAL_GREGORIAN, $monthNumber, $year);
            $day = [];

            for ($i = 1; $i <= $day_count; $i++) {
                $todayDate = $year . '-' . $monthNumber . '-' . $i;
                $detail['day_index'] = $i;
                $detail['date'] = date('Y-m-d', strtotime($todayDate));

                $query = DB::table('facility_bookings as fb')
                    ->join('orders as o', 'o.id', '=', 'fb.order_id')
                    ->join('facilities as f', 'f.id', '=', 'fb.facility_id')
                    ->join('facility_services as fs', 'fs.facility_id', '=', 'fb.facility_id')
                    ->whereIn('fb.status_id', [1, 4])
                    ->whereIn('o.order_status_id', [11, 12])
                    ->where(['o.status_id' => 4, 'o.venue_id' => $this->venueId, 'f.type' => 'ground']);
                if ($request->has('venue_service_ids')) {
                    $query = $query->whereIn('fs.venue_service_id', $request->input('venue_service_ids'));
                }
                $query = $query->whereRaw("fb.date = date('$todayDate')");
                $detail['booking'] = $query->count();


                $productType = ProductType::where(['name' => 'Facility', 'status_id' => 1])->first();
                $complementaryPaymentMethod = PaymentMethod::where(['name' => 'Complementary', 'type' => 'normal'])->first();

                $sales = DB::table('product_types as pt')
                    ->selectRaw('SUM(op.total) AS sales')
                    ->leftJoin('products as p', 'p.product_type_id', '=', 'pt.id')
                    ->join('order_items as oi', function ($query) use ($request) {
                        $query->on('oi.product_id', '=', 'p.id')
                            ->where('oi.id', DB::raw('(select id from order_items where order_items.order_id = oi.order_id LIMIT 1)'));
                        if ($request->has('venue_service_ids')) {
                            $query = $query->whereIn('oi.venue_service_id', $request->input('venue_service_ids'));
                        }
                    })
                    ->leftJoin('orders as o', 'oi.order_id', '=', 'o.id')
                    ->leftJoin('facility_bookings as fb', function ($query) {
                        $query->on('o.id', '=', 'fb.order_id')
                            ->where('fb.id', DB::raw('(select id from facility_bookings where facility_bookings.order_id = fb.order_id LIMIT 1)'));
                    })
                    ->leftJoin('order_payments as op', 'op.order_id', '=', 'o.id')
                    ->where('o.venue_id', $this->venueId)
                    ->where('pt.id', $productType->id)
                    ->whereIn('o.status_id', [4, 8])
                    ->where(function ($query) use ($complementaryPaymentMethod) {
                        $query->whereNotIn('op.payment_method_id', [$complementaryPaymentMethod->id])
                            ->orWhereNull('op.payment_method_id');
                    })
                    ->whereRaw("STR_TO_DATE(o.created_at,'%Y-%m-%d') = date('$todayDate')");


                $refund = DB::table('product_types as pt')
                    ->selectRaw('SUM(rp.amount) as refund')
                    ->leftJoin('products as p', 'p.product_type_id', '=', 'pt.id')
                    ->join('order_items as oi', function ($query) use ($request) {
                        $query->on('oi.product_id', '=', 'p.id')
                            ->where('oi.id', DB::raw('(select id from order_items where order_items.order_id = oi.order_id LIMIT 1)'));
                        if ($request->has('venue_service_ids')) {
                            $query = $query->whereIn('oi.venue_service_id', $request->input('venue_service_ids'));
                        }
                    })
                    ->leftJoin('orders as o', 'oi.order_id', '=', 'o.id')
                    ->leftJoin('facility_bookings as fb', function ($query) {
                        $query->on('o.id', '=', 'fb.order_id')
                            ->where('fb.id', DB::raw('(select id from facility_bookings where facility_bookings.order_id = fb.order_id LIMIT 1)'));
                    })
                    ->leftJoin('refunds as r', 'r.order_id', '=', 'o.id')
                    ->leftJoin('refund_payments as rp', 'rp.refund_id', '=', 'r.id')
                    ->where('o.venue_id', $this->venueId)
                    ->where('pt.id', $productType->id)
                    ->whereIn('o.status_id', [4, 8])
                    ->whereRaw("STR_TO_DATE(o.created_at,'%Y-%m-%d') = date('$todayDate')");
                // $log = Str::replaceArray('?', $query2->getBindings(), $query2->toSql());
                // Log::info('--------api testing log' . json_encode($log));
                $sales = $sales->first();
                $refund = $refund->first();
                if (!$sales) {
                    $sales = 0;
                } else {
                    $sales = $sales->sales;
                }
                if (!$refund) {
                    $refund = 0;
                } else {
                    $refund = $refund->refund;
                }

                if ($query) {
                    $detail['sales'] = $sales - $refund;
                } else {
                    $detail['sales'] = 0;
                }

                $day[] = $detail;
            }


            return response()->json(['status' => true, 'message' => 'Success', 'data' => $day], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function getAddOnProducts(Request $request)
    {
        try {

            $startTime = null;
            $endTime = null;
            if ($request->has('date')) {
                $date = $request->input('date');
                $dayOfWeek = Carbon::parse($request->input('date'))->englishDayOfWeek;
                $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();
            }
            if ($request->has('start_time')) {
                $startTime = $request->input('start_time');
            }
            if ($request->has('end_time')) {
                $endTime = $request->input('end_time');
            }
            $facilityId = $request->input('facility_id');

            $addOnProducts = Facility::with(['categories' => function ($query) use ($bitValue, $startTime, $endTime, $date) {
                $query->with(['products' => function ($query) use ($bitValue, $startTime, $endTime, $date) {

                    $query->leftJoin('product_inventories as pi', 'pi.product_id', '=', 'products.id')
                        ->leftJoin('product_inventory_rules as pir', 'pir.product_inventory_id', '=', 'pi.id')
                        ->where('products.status_id', 1)
                        ->where(function ($query) use ($bitValue, $startTime, $endTime) {
                            $query->whereRaw("(pir.weekdays & $bitValue)> 0")
                                ->where(function ($query) use ($startTime, $endTime) {
                                    $query->where('pir.start_time', '<=', $startTime);
                                    $query->where('pir.end_time', '>=', $endTime);
                                });
                        })->orWhere(function ($query) {
                            $query->orWhere(function ($query) {
                                $query->where('products.inventory_enable', 0)->where('products.status_id', 1);
                            });
                            $query->orWhere(function ($query) {
                                $query->where('products.status_id', 1)->whereNull('pir.weekdays');
                            });
                        })
                        ->select(
                            'products.id as id',
                            'products.*',
                            'pi.quantity as actual_qty',
                            'pi.quantity as quantity',
                            DB::raw("(select sum(pis.quantity) from product_inventory_sales as pis where pis.product_inventory_id = pi.id and pis.status_id = 1 and date = '$date') as sales")
                        )
                        ->groupBy('products.id');
                }])->whereIn('categories.status_id', [1, 14]);
            }])->where(['venue_id' => $this->venueId, 'status_id' => 1, 'id' => $facilityId])->first();

            return response()->json(['status' => true, 'message' => 'Success', 'data' => $addOnProducts], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function editBooking(Request $request)
    {
        try {

            $rules = [
                'order_id' => 'exists:orders,id',
                'products' => 'sometimes|array',
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }

            DB::beginTransaction();
            $orderId = $request->input('order_id');
            $currentOrder = Order::with('invoiceItem')->find($orderId);
            $customer = Customer::find($currentOrder->customer_id);
            if ($request->has('facility_booking_id')){
                $facilityBooking = FacilityBooking::where('order_id',$orderId)->find($request->input('facility_booking_id'));
                $facilityId = $facilityBooking->facility_id;
                $fac = Facility::where('id',$facilityId )->first();
                if($fac->per_capacity == 1){
                    $request->request->add(['facility_id'=>$facilityId]);
                    $facilityAvailability = FacilityHelper::facilityAvailability($request, $request->input('venue_service_id'), $this->venueId);
                    if ($facilityAvailability->count() == 0) {
                        throw new \Exception('Facility not available');
                    }

                    if ($fac->is_enable_per_day_capacity == 0) {
                        $total_attendance = FacilityBooking::where('facility_id', $facilityId)
                            ->where('start_time', $request->input('start_time'))
                            ->where('end_time', $request->input('end_time'))
                            ->where('date', $request->date)
                            ->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                            ->sum('attendance');

                        $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)
                            ->where('start_time', $request->input('start_time'))
                            ->where('end_time', $request->input('end_time'))
                            ->where('date', $request->date)
                            ->first();
                    } else {
                        $total_attendance = FacilityBooking::where('facility_id', $facilityId)
                            ->where('date', $request->date)
                            ->whereIn('status_id', [1, 4, 5, 6, 12, 14, 21, 7])
                            ->sum('attendance');
                        $facilityThresholdEmailLog = FacilityThresholdEmailLog::where('facility_id', $facilityId)
                            ->where('date', $request->date)
                            ->first();
                    }

                    $total_attendance = (int)$total_attendance + $facilityBooking->attendance;

                    $facility_capacity = isset($fac->capacity) ? (int)$fac->capacity : 0;

                    if ($fac && $total_attendance > $facility_capacity) {
                        throw new \Exception('Trying to book more than available capacity');
                    }

                    /** send email threshold */
                    if (!$facilityThresholdEmailLog
                        && $fac
                        && $fac->is_enable_email_threshold == 1
                        && $fac->online_threshold > 0
                        && $fac->email_threshold_recipient != ""
                        && $total_attendance >= $fac->online_threshold

                    ) {
                        $facilityThresholdEmailLog = new FacilityThresholdEmailLog();
                        $facilityThresholdEmailLog->facility_id = $facilityId;
                        $facilityThresholdEmailLog->date = $request->date;
                        $facilityThresholdEmailLog->start_time = $request->input('start_time');
                        $facilityThresholdEmailLog->end_time = $request->input('end_time');
                        $facilityThresholdEmailLog->save();
                        $result = MailHelper::sendEmail($fac->email_threshold_recipient, new FacilityBookingThresholdEmail($fac, $facilityThresholdEmailLog, $customer, $this->venue));
                    }
                    $date = $request->input('date');
                    $facilityBooking->date = $date;
                    $facilityBooking->start_time = $request->input('start_time');
                    $facilityBooking->end_time = $request->input('end_time');
                    $facilityBooking->save();
                    $currentOrder->order_date = $date;
                    $dateTime = Carbon::parse($date. " ". $request->input('start_time'));
                    $currentOrder->order_datetime = $dateTime->format("Y-m-d H:i:s");
                    $currentOrder->save();

                    if($request->boolean('notify_customers')){
                        SendFacilityBookingRescheduleMailJob::dispatch($facilityBooking,$this->venue);
                    }

                    $customer = $currentOrder->customer ?? null;
                    $email = $customer->customer_contact->email ?? null;
                    $facilityBooking->customer_name = $customer->first_name . ' ' . $customer->last_name;
                    $facilityBooking->customer_email = $email;
                    $facilityBooking->facility_name = $facilityBooking->facility->name;
                    SendOperatorFacilityBookingRescheduleMailJob::dispatch($facilityBooking,$this->venue);
                }
            }
            if ($request->has('products') && count($request->products) > 0){
                $discount = null;
                if ($request->has('promotion_code')) {
                    $discount = DiscountHelper::getBenefits($this->venueId, $request->input('promotion_code'), 'promotion', $customer->mobile);
                    $discount->type = 'promotion';
                }
                if ($request->has('card_number')) {
                    $discount = DiscountHelper::getBenefits($this->venueId, $request->input('card_number'), 'membership', $customer->mobile);
                    $discount->type = 'membership';
                }

                $order = OrderHelper::createOrUpdateOrder([
                    'order_date' => $currentOrder->order_date,
                    'order_datetime' => $currentOrder->order_datetime,
                    'invoice_seq_no' => $this->generateSequence('O'),
                    'customer_id' => $currentOrder->customer_id,
                    'status_id' => 5,
                    'order_type_id' => 7,
                    'order_seq_no' => null,
                    'order_status_id' => 11,
                    'venue_id' => $this->venueId,
                    'parent_order_id' => $currentOrder->id,
                    'initial_order_id' => $currentOrder->initial_order_id ? $currentOrder->initial_order_id : $currentOrder->id
                ]);

                $totalPrice = 0;
                $totalTax = 0;
                $totalAmount = 0;

                if ($request->has('products') ) {
                    foreach ($request->input('products') as $key => $item) {
                        $item = (object)$item;
                        $product = Product::with(['productCategory'])->where('id', $item->product_id)->first();

                        if (!isset($product)) {
                            $categories = Category::where(['venue_service_id' => $request->input('venue_service_id'), 'name' => 'open_product']);
                            if ($categories->exists()) {
                                $categories = $categories->first();
                            } else {
                                $categories = new Category();
                                $categories->name = 'open_product';
                                $categories->venue_service_id = $request->input('venue_service_id');
                                $categories->status_id = 14;
                                $categories->save();
                            }
                            $product = new Product;
                            $product->venue_id = $this->venueId;
                            $product->name = $item->name;
                            $product->price = $item->price / $item->quantity;
                            $product->total_price = $item->total_price;
                            $product->tax_type_id = $item->tax ? 1 : 2;
                            $product->product_type_id = 6;
                            $product->tax_amount = $item->tax ? $item->tax : 0;
                            $product->save();
                            $productCategory = ProductCategory::updateOrCreate(['product_id' => $product->id, 'category_id' => $categories->id]);
                        }

                        $quantity = $item->quantity;
                        $price = $product->price * $quantity;
                        $tax = $product->tax_amount * $quantity;
                        $total = $price + $tax;
                        $totalPrice += $price;
                        $totalTax += $tax;
                        $totalAmount += $total;
                        $orderItem = OrderHelper::createOrUpdateOrderItem([
                            'order_id' => $order->id,
                            'price' => $price,
                            'tax' => $tax,
                            'product_id' => $product->id,
                            'quantity' => $quantity,
                            'status_id' => 5,
                            'total' => $total,
                            'venue_service_id' => $request->input('venue_service_id')
                        ]);

                        if ($discount != null) {
                            $orderItem->category_id = $product->productCategory->category_id;
                            DiscountHelper::applyProductDiscount($discount->benefits, $orderItem);
                            unset($orderItem->category_id);
                        }
                    }

                    FacilityHelper::updateProductInventory($product, $quantity, $currentOrder->order_date, $orderItem);
                }

                $order->price = $totalPrice;
                $order->tax = $totalTax;
                $order->total = $totalAmount;
                $order->save();

                if ($discount != null) {
                    DiscountHelper::applyOrderDiscount($order->id, $discount->type, $discount->id);
                } else if ($discount == null) {
                    DiscountHelper::removeDiscount($order->id);
                }

                if ($currentOrder->invoice_generated == 1) {
                    if ($currentOrder->status_id == 5) {
                        InvoiceHelper::updateInvoiceUsingOrder($currentOrder, [$order->id]);
                    } else {
                        $order->refresh();
                        $originalInvoice = $currentOrder->invoiceItem ? $currentOrder->invoiceItem->invoice_id : null;
                        $initialInvoice = Invoice::find($originalInvoice);
                        $invoiceController = (new InvoiceController());
                        $invoice = InvoiceHelper::createOrUpdateInvoice([
                            'invoice_seq_no' => $invoiceController->generateInvoiceSequence('I'),
                            'invoice_date' => $currentOrder->order_date,
                            'price' => $order->price,
                            'tax' => $order->tax,
                            'total' => $order->total,
                            'venue_id' => $this->venueId,
                            'customer_id' => $currentOrder->customer_id,
                            'parent_invoice_id' => $originalInvoice,
                            'initial_invoice_id' => $initialInvoice->initial_invoice_id ?? $originalInvoice,
                            'status_id' => 5,
                            'invoice_status_id' => 11,
                        ]);

                        $invoiceController->checkOrderAlreadyExistInInvoice([$order->id]);
                        InvoiceHelper::createOrUpdateInvoiceItem([
                            'order_id' => $order->id,
                            'invoice_id' => $invoice->id,
                            'price' => $order->price,
                            'tax' => $order->tax,
                            'total' => $order->total,
                            'status_id' => $order->status_id,
                        ]);
                        $order->invoice_generated = 1;
                        $order->save();
                    }
                }
            }else{
                $order = $currentOrder;
            }

            Logging::create([
                'venue_id' => $this->venueId,
                'user_id' => $this->userId,
                'target_id' => $order->id,
                'model' => get_class($order),
                'table' => $order->getTable(),
                'action' => 'Create',
                'description' =>  "{$this->user->first_name} {$this->user->last_name} created order '{$order->invoice_seq_no}'.",
            ]);


            DB::commit();
            return response()->json(['status' => true, 'message' => 'Success', 'data' => $order], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Function for finding best possible combination of rental products
     *
     * @param Request $request
     * @return Response
     */
    public function getFacilityRentals(Request $request)
    {
        try {
            $rules = [
                'date' => 'date_format:Y-m-d',
                'facility_id' => 'exists:facilities,id',
                'start_time' => 'required|date_format:H:i:s',
                'end_time' => 'required|date_format:H:i:s|after:start_time',
            ];

            $overNightBookingEnable = 1;
            if ($request->has('venue_service_id')) {
                $venueServiceConfig = VenueServiceConfiguration::where('venue_service_id', $request->input('venue_service_id'))->first();
                if ($venueServiceConfig) {
                    $overNightBookingEnable = $venueServiceConfig->enable_over_night_booking;
                }
            }
            if ($this->checkPermissions('fob:allow_read') && $overNightBookingEnable) {
                $rules['end_time'] = 'required|date_format:H:i:s';
            }

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }

            $endDate = null;
            $rentals1 = [];
            $facilityId = $request->input('facility_id');
            $startTime = $request->input('start_time');
            $endTime = $request->input('end_time');
            $carbonEndTime = Carbon::parse($endTime);
            $carbonStartTime = Carbon::parse($startTime);
            if ($carbonEndTime->lt($carbonStartTime)) {
                $endDate = Carbon::createFromFormat('Y-m-d', $request->input('date'))->addDay()->toDateString();
            }

            if ($endDate) {
                $endTime2 = $endTime;
                $request1 = new Request(['date' => $request->input('date'), 'facility_id' => $facilityId, 'same_day' => 1]);
                $facilityController = (new FacilityController());
                $sameDayTiming = $facilityController->getFacilityTimings($request1);
                if (!json_decode($sameDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get same day end time');
                } else {
                    $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                }

                $request2 = new Request(['date' => $endDate, 'facility_id' => $facilityId, 'same_day' => 1]);
                $nextDayTiming = $facilityController->getFacilityTimings($request2);
                if (!json_decode($nextDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get next day start time');
                } else {
                    $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                }
            }
            $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime)); //duration from given start & end time
            if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                $duration += 1;
            }

            $dayOfWeek = Carbon::parse($request->input('date'))->englishDayOfWeek;
            $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first(); // Date bit value for rental checking (table weekdays)

            $rentals = FacilityBookingHelper::getRentalProducts($facilityId, $duration, $startTime, $endTime, $bitValue, $request->input('date'));

            if ($endDate) {
                $dayOfWeek = Carbon::parse($endDate)->englishDayOfWeek;
                $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first(); // Date bit value for rental checking (table weekdays)
                $duration2 = Carbon::parse($startTime2)->diffInMinutes(Carbon::parse($endTime2)); //duration from given start & end time
                if ($duration2 % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $duration2 += 1;
                }
                $rentals1 = FacilityBookingHelper::getRentalProducts($facilityId, $duration2, $startTime2, $endTime2, $bitValue, $endDate);
            }
            if (count($rentals1) > 0) {

                $resultArray = [];
                // Loop through each product in the first array
                foreach ($rentals as $item1) {
                    // Loop through each product in the second array
                    foreach ($rentals1 as $item2) {
                        // Combine the products from both arrays
                        if (strtolower(gettype($item1['products'])) == 'array') {
                            if (strtolower(gettype($item2['products'])) == 'array') {
                                $combinedProducts = array_merge($item1['products'], $item2['products']);
                            } else {
                                $combinedProducts = array_merge($item1['products'], $item2['products']->toArray());
                            }
                        } else {
                            if (strtolower(gettype($item2['products'])) == 'array') {
                                $combinedProducts = $item1['products']->merge(collect($item2['products']));
                            } else {
                                $combinedProducts = $item1['products']->merge($item2['products']);
                            }
                        }

                        // Calculate the total price
                        $totalPrice = $item1['total_price'] + $item2['total_price'];

                        // Create the combined result
                        $combinedResult = [
                            "total_price" => $totalPrice,
                            "products" => $combinedProducts
                        ];

                        // Add the combined result to the result array
                        $resultArray[] = array_merge([], $combinedResult);
                    }
                }
                $resultArray = $this->reduceRedundantProducts($resultArray);
                $rentals = $resultArray;
            }
            return response()->json(['status' => true, 'message' => "Success", 'data' => $rentals], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }


    public function reduceRedundantProducts($data)
    {
        // Define an array to store merged products
        $mergedProducts = [];

        $result = [];

        foreach ($data as $key => $item) {
            $mergedProducts = [];
            foreach ($item['products'] as $index => $product) {


                $id = $product->id;
                // If the product with this ID already exists in the merged array, increment quantity and adjust price
                if (isset($mergedProducts[$id])) {
                    $mergedProducts[$id]->quantity += $product->quantity;
                    $mergedProducts[$id]->price += $product->price;
                    $mergedProducts[$id]->duration += $product->duration;
                    if (isset($mergedProducts[$id]->tax_amount)) {
                        $mergedProducts[$id]->tax_amount += $product->tax_amount;
                    }
                    if (isset($mergedProducts[$id]->total_tax_amount)) {
                        $mergedProducts[$id]->total_tax_amount += $product->total_tax_amount;
                    }
                } else {
                    // If the product doesn't exist, add it to the merged array
                    $mergedProducts[$id] = clone $product;
                }
            }
            $result[$key] = array_merge([], $item);
            $result[$key]['products'] = array_values($mergedProducts);
        }

        //        info('$result');
        //        info($result);
        //        // Replace the 'products' array with the merged products
        //        foreach ($data as &$item) {
        //            $mergedItemProducts = [];
        //            foreach ($item['products'] as $product) {
        //                $id = $product->id;
        //                if (isset($mergedProducts[$id])) {
        //                    $mergedItemProducts[] = $mergedProducts[$id];
        //                    unset($mergedProducts[$id]); // Remove the product from mergedProducts array to avoid duplicate entry
        //                }
        //            }
        //            $item['products'] = $mergedItemProducts;
        //        }

        return $result;
    }

    /**
     * Function for finding best possible combination of rental products
     *
     * @param Request $request
     * @return Response
     */
    public function getMultipleDatesRentals(Request $request)
    {
        try {
            $availableDates = $request->input('dates');
            $bookingIds = [];
            foreach ($availableDates as $key => $date) {
                $date = (object)$date;
                $bookingIds[] = $date->booking_id;
            }

            $product = FacilityBookingItem::whereIn('facility_booking_id', $bookingIds)
                ->whereNotIn('facility_booking_items.status_id', [2])
                ->join('order_items as oi', 'oi.id', '=', 'facility_booking_items.order_item_id')
                ->join('orders as o', 'o.id', '=', 'oi.order_id')
                ->join('products as p', 'p.id', '=', 'oi.product_id')
                ->join('product_categories as pc', 'pc.product_id', '=', 'p.id')
                ->select(
                    'p.id',
                    'p.id as product_id',
                    'p.price as product_price',
                    DB::raw('sum(facility_booking_items.price) as price'),
                    'p.image as image_path',
                    'p.product_type_id',
                    'p.name',

                    DB::raw('if(facility_booking_items.quantity < 1 , sum(facility_booking_items.tax) , facility_booking_items.tax/facility_booking_items.quantity) as tax_amount'),
                    'pc.category_id',
                    DB::raw('sum(facility_booking_items.tax) as total_tax_amount'),
                    DB::raw('sum(facility_booking_items.quantity) as quantity')
                )
                ->where('o.venue_id', $this->venueId)
                ->where('pc.status_id', 1)
                ->groupBy('p.id')
                ->get();
            // $productObj = $this->getProductWithDifferentTimingTemplates($facilityId, $startTime, $endTime, $availableDates);
            return response()->json(['status' => true, 'message' => "Success", 'data' => ['products' => $product]], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Reschedule facilities booking
     *
     * @return Response
     */
    public function getRescheduleBookingDetails(Request $request, $id)
    {
        $rules = [
            'date' => 'date_format:Y-m-d',
            'venue_service_id' => 'required|exists:venue_services,id',
            'start_time' => 'date_format:H:i:s',
            'end_time' => 'date_format:H:i:s|after:start_time',
        ];

        $isEnableOverNight = false;
        if ($request->has('enable_overnight_booking') && $request->input('enable_overnight_booking')) {
            $rules['end_time'] = 'date_format:H:i:s';
            $isEnableOverNight = true;
        }

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        try {

            $data = $this->getBookingDetails($id);
            $rescheduleData = clone $data;
            $currentProducts = FacilityBookingItem::where(['facility_booking_id' => $rescheduleData->id])
                ->whereNotIn('facility_booking_items.status_id', [2])
                ->join('order_items as oi', 'oi.id', '=', 'facility_booking_items.order_item_id')
                ->leftJoin('order_item_discounts as oid', 'oi.id', '=', 'oid.order_item_id')
                ->join('products as p', 'p.id', '=', 'oi.product_id')
                ->select(
                    'oi.id',
                    DB::raw('SUM(facility_booking_items.quantity) as quantity'),
                    DB::raw('SUM(oid.actual_price) as actual_price'),
                    DB::raw('SUM(facility_booking_items.price) as price'),
                    'p.tax_amount',
                    DB::raw('SUM(oid.actual_total) as actual_total'),
                    DB::raw('SUM(facility_booking_items.total) as total'),
                    'oi.product_id',
                    'p.name'
                )
                ->groupBy('oi.product_id')
                ->havingRaw('quantity > 0')
                ->get();
            // $currentProducts = FacilityBookingHelper::getProductWithDifferentTimingTemplates($repeatedFacilityId, $repeatStartTime, $repeatEndTime, [$repeatDate]);
            $currentOrderTotal = 0;
            $tax = 0;
            $price = 0;
            foreach ($currentProducts as $item) {
                $tax += ($item->tax_amount * $item->quantity);
                $price += $item->price;
                $currentOrderTotal += ($tax + $price);
                $item->rental = true;
            }
            unset($rescheduleData->order->items);
            $rescheduleData->order->items = $currentProducts;
            $rescheduleData->order->tax = $tax;
            $rescheduleData->order->price = $price;
            $rescheduleData->order->total = $currentOrderTotal;
            // }

            $date = $rescheduleData->date;
            if ($request->has('date')) {
                $date = $request->input('date');
            }

            $facilityId = $rescheduleData->facility_id;
            $rescheduleData->min_booking_time = Facility::where('id', $facilityId)->pluck('min_booking_time')->first();


            if ($request->has('facility_id')) {
                $facilityId = $request->input('facility_id');
            }

            $startTime = $rescheduleData->start_time;
            if ($request->has('start_time')) {
                $startTime = $request->input('start_time');
            }

            $endTime = $rescheduleData->end_time;
            if ($request->has('end_time')) {
                $endTime = $request->input('end_time');
            }

            $endDate = null;
            $rentals1 = [];
            $venueServiceId = $request->input('venue_service_id');

            $productTypeID = ProductType::where('name', 'Facility')->pluck('id')->first();
            $venueServiceConfiguration = VenueServiceConfiguration::firstOrCreate(['venue_service_id' => $venueServiceId, 'product_type_id' => $productTypeID]);


            $dayOfWeek = Carbon::parse($date)->englishDayOfWeek;
            $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();

            $rescheduleData->product_combinations = [];

            if (
                $request->has('facility_id') &&
                ($request->input('facility_id') != $rescheduleData->facility_id ||
                    Carbon::parse($date)->notEqualTo(Carbon::parse($rescheduleData->date)) ||
                    Carbon::parse($startTime)->notEqualTo(Carbon::parse($rescheduleData->start_time)) ||
                    Carbon::parse($endTime)->notEqualTo(Carbon::parse($rescheduleData->end_time))
                )
            ) {
                $carbonEndTime = Carbon::parse($endTime);
                $carbonStartTime = Carbon::parse($startTime);
                if ($carbonEndTime->lt($carbonStartTime)) {
                    $endDate = Carbon::createFromFormat('Y-m-d', $date)->addDay()->toDateString();
                }
                if ($endDate) {
                    $endTime2 = $endTime;
                    $request1 = new Request(['date' => $request->input('date'), 'facility_id' => $facilityId, 'same_day' => 1]);
                    $facilityController = (new FacilityController());
                    $sameDayTiming = $facilityController->getFacilityTimings($request1);
                    if (!json_decode($sameDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get same day end time');
                    } else {
                        $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                    }
                    $request2 = new Request(['date' => $endDate, 'facility_id' => $facilityId, 'same_day' => 1]);
                    $nextDayTiming = $facilityController->getFacilityTimings($request2);
                    if (!json_decode($nextDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get next day start time');
                    } else {
                        $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                    }
                }

                if($endDate && $isEnableOverNight){
                    if ($carbonEndTime->lt($carbonStartTime)) {
                        $carbonEndTime->addDay();
                    }
                    $overAllDuration = $carbonStartTime->diffInMinutes($carbonEndTime);
                    $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                    //$duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                }else{
                    $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                    $overAllDuration = $duration;
                }
                if ($overAllDuration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $overAllDuration += 1;
                }
                if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $duration += 1;
                }

                if ($overAllDuration < $rescheduleData->min_booking_time) {
                    throw new Exception('Minimum booking time is: ' . $rescheduleData->min_booking_time . ' minutes');
                }
                // check product is open base product if it is than don't change
                $openRental = RentalProduct::where('venue_service_id',$venueServiceId)->where('product_id',$currentProducts[0]->product_id)->where('status_id',14)->first();
                $isOpenBaseProduct = false;
                if($openRental){
                    $isOpenBaseProduct = true;
                    $products = DB::table('products as p')->select(
                        'p.id',
                        'p.price as product_price',
                        'p.price',
                        'p.image as image_path',
                        'p.product_type_id',
                        'p.name',
                        'pc.category_id',
                        'rp.duration',
                        'rp.is_full_day as is_full_day',
                        DB::raw('p.tax_amount AS total_tax_amount'),
                        DB::raw('p.tax_amount AS tax_amount'),
                        DB::raw('1 as quantity'),
                        DB::raw('1 as is_repeatable')
                    )->join('product_categories as pc', 'pc.product_id', '=', 'p.id')
                        ->join('rental_products as rp','rp.product_id','p.id')
                        ->where(['rp.venue_service_id' => $venueServiceId,'rp.product_id' => $currentProducts[0]->product_id,'p.id' => $currentProducts[0]->product_id])
                        ->where('rp.status_id',14)
                        ->get();
                    $overlap = false;
                   $rentals = FacilityBookingHelper::getProductCombinations($products, $duration, $overlap);
                }else{
                    $rentals = FacilityBookingHelper::getRentalProducts($facilityId, $duration, $startTime, $endTime, $bitValue, $date);
                }
                if ($endDate) {
                    $dayOfWeek = Carbon::parse($endDate)->englishDayOfWeek;
                    $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first(); // Date bit value for rental checking (table weekdays)
                    $duration2 = Carbon::parse($startTime2)->diffInMinutes(Carbon::parse($endTime2)); //duration from given start & end time
                    if ($duration2 % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $duration2 += 1;
                    }
                    if($isOpenBaseProduct){
                        $overlap = false;
                        $rentals = FacilityBookingHelper::getProductCombinations($products, $overAllDuration, $overlap);
                    }else{
                        $rentals1 = FacilityBookingHelper::getRentalProducts($facilityId, $duration2, $startTime2, $endTime2, $bitValue, $endDate);
                    }
                    if (count($rentals1) > 0) {
                        $resultArray = [];
                        // Loop through each product in the first array
                        foreach ($rentals as $item1) {
                            // Loop through each product in the second array
                            foreach ($rentals1 as $item2) {
                                // Combine the products from both arrays
                                if (strtolower(gettype($item1['products'])) == 'array') {
                                    if (strtolower(gettype($item2['products'])) == 'array') {
                                        $combinedProducts = array_merge($item1['products'], $item2['products']);
                                    } else {
                                        $combinedProducts = array_merge($item1['products'], $item2['products']->toArray());
                                    }
                                } else {
                                    if (strtolower(gettype($item2['products'])) == 'array') {
                                        $combinedProducts = $item1['products']->merge(collect($item2['products']));
                                    } else {
                                        $combinedProducts = $item1['products']->merge($item2['products']);
                                    }
                                }
                                // Calculate the total price
                                $totalPrice = $item1['total_price'] + $item2['total_price'];
                                // Create the combined result
                                $combinedResult = [
                                    "total_price" => $totalPrice,
                                    "products" => $combinedProducts
                                ];
                                // Add the combined result to the result array
                                $resultArray[] = $combinedResult;

                                $resultArray = $this->reduceRedundantProducts($resultArray);
                                $rentals = $resultArray;
                            }
                        }
                    }
                }
                $rescheduleData->product_combinations = $rentals;
            }
            $rescheduleData->date = $date;
            $rescheduleData->end_time = (isset($endTime2) && $endTime2) ? $endTime2 : $endTime;
            $rescheduleData->start_time = $startTime;
            $rescheduleData->facility_id = (int)$facilityId;

            $increment = $venueServiceConfiguration->time_increment;
            $perCapacity = Facility::where('id', $facilityId)->pluck('per_capacity')->first();
            $facility = $this->getFacilityWithProducts($facilityId, $startTime, $endTime, $bitValue, $date);

            if (($facility && $facility->facilityRentals->count() > 0) || $perCapacity == 1) {

                $rentalPackage = FacilityRental::where(['facility_id' => $facilityId, 'status_id' => 1])->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")->orderBy('start_time', 'asc')->get();

                if ($perCapacity == 1) {
                    $facilityStartTime = Carbon::parse($facility->opening_time);
                    $facilityEndTime = Carbon::parse($facility->closing_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                } else {
                    $facilityStartTime = Carbon::parse($rentalPackage->first()->start_time);
                    $facilityEndTime = Carbon::parse($rentalPackage->last()->end_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                }

                $times = [];
                $currentBookingIndex = false;
                $bookingCount = $bookings->count();
                if ($bookingCount > 0) {
                    $currentBookingIndex = 0;
                    $currentBooking = $bookings[$currentBookingIndex];
                    $bookingStartTime = Carbon::parse($currentBooking->start_time);
                    $bookingEndTime = Carbon::parse($currentBooking->start_time);
                }

                $intervals = CarbonInterval::minutes($increment)->toPeriod($facilityStartTime, $facilityEndTime);
                foreach ($intervals as $time) {
                    if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                        if ($time->greaterThanOrEqualTo($bookingEndTime)) {
                            $currentBookingIndex += 1;
                            $currentBooking = $bookings[$currentBookingIndex];
                            $bookingStartTime = Carbon::parse($currentBooking->start_time);
                            $bookingEndTime = Carbon::parse($currentBooking->start_time);
                        }
                        if ($time->between($bookingStartTime, $bookingEndTime)) continue;
                    }
                    $times[] = (object)['time' => $time->format('H:i:s'), 'formatted' => $time->format('h:i a')];
                }
                if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                    if (!$facilityEndTime->between($bookingStartTime, $bookingEndTime)) {
                        $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                    }
                } else {
                    $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                }
                $rescheduleData->times = $times;
            } else {
                $facilityController = (new FacilityController());
                $request1 = new Request(['date' => $date, 'facility_id' => $facilityId, 'same_day' => 1]);
                $sameDayTiming = $facilityController->getFacilityTimings($request1);
                if (!json_decode($sameDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get same day timings');
                } else {
                    $startTime = json_decode($sameDayTiming->getContent())->start_time->start_time;
                    $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;

                    $bookings = FacilityBooking::where('start_time', '<=', $startTime)->where('start_time', '>=', $endTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();


                    $times = [];
                    $currentBookingIndex = false;

                    $bookingCount = $bookings->count();
                    if ($bookingCount > 0) {
                        $currentBookingIndex = 0;
                        $currentBooking = $bookings[$currentBookingIndex];
                        $bookingStartTime = Carbon::parse($currentBooking->start_time);
                        $bookingEndTime = Carbon::parse($currentBooking->start_time);
                    }

                    $intervals = CarbonInterval::minutes($increment)->toPeriod($startTime, $endTime);
                    foreach ($intervals as $time) {
                        if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                            if ($time->greaterThanOrEqualTo($bookingEndTime)) {
                                $currentBookingIndex += 1;
                                $currentBooking = $bookings[$currentBookingIndex];
                                $bookingStartTime = Carbon::parse($currentBooking->start_time);
                                $bookingEndTime = Carbon::parse($currentBooking->start_time);
                            }
                            if ($time->between($bookingStartTime, $bookingEndTime)) continue;
                        }
                        $times[] = (object)['time' => $time->format('H:i:s'), 'formatted' => $time->format('h:i a')];
                    }
                    if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                        if (!$endTime->between($bookingStartTime, $bookingEndTime)) {
                            $times[] = (object)['time' => Carbon::parse($endTime)->format('H:i:s'), 'formatted' => Carbon::parse($endTime)->format('h:i a')];
                        }
                    } else {
                        $times[] = (object)['time' => Carbon::parse($endTime)->format('H:i:s'), 'formatted' => Carbon::parse($endTime)->format('h:i a')];
                    }
                    $rescheduleData->times = $times;
                }
            }

            $message = "Success";
            return response()->json(['status' => true, 'message' => $message, 'data' => $rescheduleData], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }
    /**
     * Reschedule facilities booking
     *
     * @return Response
     */
    public function getRescheduleCapacityBookingDetails(Request $request, $id)
    {
        $rules = [
            'date' => 'date_format:Y-m-d',
            'venue_service_id' => 'required|exists:venue_services,id',
            'start_time' => 'date_format:H:i:s',
            'end_time' => 'date_format:H:i:s|after:start_time',
        ];

        if ($request->has('enable_overnight_booking') && $request->input('enable_overnight_booking')) {
            $rules['end_time'] = 'date_format:H:i:s';
        }

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        try {

            $data = $this->getBookingDetails($id);
            $rescheduleData = clone $data;
            $currentProducts = FacilityBookingItem::where(['facility_booking_id' => $rescheduleData->id])
                ->whereNotIn('facility_booking_items.status_id', [2])
                ->join('order_items as oi', 'oi.id', '=', 'facility_booking_items.order_item_id')
                ->leftJoin('order_item_discounts as oid', 'oi.id', '=', 'oid.order_item_id')
                ->join('products as p', 'p.id', '=', 'oi.product_id')
                ->select(
                    'oi.id',
                    DB::raw('SUM(facility_booking_items.quantity) as quantity'),
                    DB::raw('SUM(oid.actual_price) as actual_price'),
                    DB::raw('SUM(facility_booking_items.price) as price'),
                    'p.tax_amount',
                    DB::raw('SUM(oid.actual_total) as actual_total'),
                    DB::raw('SUM(facility_booking_items.total) as total'),
                    'oi.product_id',
                    'p.name'
                )
                ->groupBy('oi.product_id')
                ->havingRaw('quantity > 0')
                ->get();
            // $currentProducts = FacilityBookingHelper::getProductWithDifferentTimingTemplates($repeatedFacilityId, $repeatStartTime, $repeatEndTime, [$repeatDate]);
            $currentOrderTotal = 0;
            $tax = 0;
            $price = 0;
            foreach ($currentProducts as $item) {
                $tax += ($item->tax_amount * $item->quantity);
                $price += $item->price;
                $currentOrderTotal += ($tax + $price);
                $item->rental = true;
            }
            unset($rescheduleData->order->items);
            $rescheduleData->order->items = $currentProducts;
            $rescheduleData->order->tax = $tax;
            $rescheduleData->order->price = $price;
            $rescheduleData->order->total = $currentOrderTotal;
            // }

            $date = $rescheduleData->date;
            if ($request->has('date')) {
                $date = $request->input('date');
            }

            $facilityId = $rescheduleData->facility_id;
            $rescheduleData->min_booking_time = Facility::where('id', $facilityId)->pluck('min_booking_time')->first();


            if ($request->has('facility_id')) {
                $facilityId = $request->input('facility_id');
            }

            $startTime = $rescheduleData->start_time;
            if ($request->has('start_time')) {
                $startTime = $request->input('start_time');
            }

            $endTime = $rescheduleData->end_time;
            if ($request->has('end_time')) {
                $endTime = $request->input('end_time');
            }

            $endDate = null;
            $rentals1 = [];
            $venueServiceId = $request->input('venue_service_id');

            $productTypeID = ProductType::where('name', 'Facility')->pluck('id')->first();
            $venueServiceConfiguration = VenueServiceConfiguration::firstOrCreate(['venue_service_id' => $venueServiceId, 'product_type_id' => $productTypeID]);


            $dayOfWeek = Carbon::parse($date)->englishDayOfWeek;
            $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();

            $rescheduleData->product_combinations = [];

            if (
                $request->has('facility_id') &&
                ($request->input('facility_id') != $rescheduleData->facility_id ||
                    Carbon::parse($date)->notEqualTo(Carbon::parse($rescheduleData->date)) ||
                    Carbon::parse($startTime)->notEqualTo(Carbon::parse($rescheduleData->start_time)) ||
                    Carbon::parse($endTime)->notEqualTo(Carbon::parse($rescheduleData->end_time))
                )
            ) {

                $carbonEndTime = Carbon::parse($endTime);
                $carbonStartTime = Carbon::parse($startTime);


                $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $duration += 1;
                }

                if ($duration < $rescheduleData->min_booking_time) {
                    throw new Exception('Minumum booking time is: ' . $rescheduleData->min_booking_time . ' minutes');
                }


                $rentals = FacilityBookingHelper::getRentalProducts($facilityId, $duration, $startTime, $endTime, $bitValue, $date);

                $rescheduleData->product_combinations = $rentals;
            }
            $rescheduleData->date = $date;
            $rescheduleData->end_time = $endTime;
            $rescheduleData->start_time = $startTime;
            $rescheduleData->facility_id = (int)$facilityId;

            $increment = $venueServiceConfiguration->time_increment;
            $perCapacity = Facility::where('id', $facilityId)->pluck('per_capacity')->first();
            $facility = $this->getFacilityWithProducts($facilityId, $startTime, $endTime, $bitValue, $date);

            if ($facility && $facility->facilityRentals->count() > 0 || $perCapacity == 1) {

//                $rentalPackage = FacilityRental::where(['facility_id' => $facilityId, 'status_id' => 1])->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")->orderBy('start_time', 'asc')->get();
                $rentalPackage = FacilityRental::where('facility_id', $facilityId)
                    ->where('status_id', 1)
                    ->where(function($query) use ($bitValue, $date) {
                        // If is_seasonal is 1, add date range condition
                        $query->where(function($q) use ($bitValue,$date) {
                            $q->where('is_seasonal', 1)
                                ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")
                                ->whereDate('start_date', '<=', $date)
                                ->whereDate('end_date', '>=', $date);
                        })->orWhere(function($q) use ($bitValue) {
                            // If is_seasonal is 0, apply the regular condition
                           $q->where('is_seasonal', 0)->whereRaw("(facility_rentals.weekdays & $bitValue) > 0");
                        });
                    })
                    ->orderBy('start_time', 'asc')
                    ->get();

                if ($perCapacity == 1) {
//                    info($facility->opening_time);
//                    info($facility->closing_time);
                    $facilityStartTime = Carbon::parse($facility->opening_time);
                    $facilityEndTime = Carbon::parse($facility->closing_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                } else {
                    $facilityStartTime = Carbon::parse($rentalPackage->first()->start_time);
                    $facilityEndTime = Carbon::parse($rentalPackage->last()->end_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                }

                $times = [];
                $currentBookingIndex = false;
                $bookingCount = $bookings->count();
                if ($bookingCount > 0) {
                    $currentBookingIndex = 0;
                    $currentBooking = $bookings[$currentBookingIndex];
                    $bookingStartTime = Carbon::parse($currentBooking->start_time);
                    $bookingEndTime = Carbon::parse($currentBooking->start_time);
                }

                $intervals = CarbonInterval::minutes($increment)->toPeriod($facilityStartTime, $facilityEndTime);
                foreach ($intervals as $time) {
                    if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                        if ($time->greaterThanOrEqualTo($bookingEndTime)) {
                            $currentBookingIndex += 1;
                            $currentBooking = $bookings[$currentBookingIndex];
                            $bookingStartTime = Carbon::parse($currentBooking->start_time);
                            $bookingEndTime = Carbon::parse($currentBooking->start_time);
                        }
                        if ($time->between($bookingStartTime, $bookingEndTime)) continue;
                    }
                    $times[] = (object)['time' => $time->format('H:i:s'), 'formatted' => $time->format('h:i a')];
                }
                if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                    if (!$facilityEndTime->between($bookingStartTime, $bookingEndTime)) {
                        $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                    }
                } else {
                    $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                }
                $rescheduleData->times = $times;
            }
            $message = "Success";
            return response()->json(['status' => true, 'message' => $message, 'data' => $rescheduleData], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Reschedule facilities booking
     *
     * @return Response
     */
    public function getRescheduleMultiBookingDetails(Request $request, $id)
    {
        $rules = [
            'date' => 'date_format:Y-m-d',
            'venue_service_id' => 'required|exists:venue_services,id',
            'start_time' => 'date_format:H:i:s',
            'end_time' => 'date_format:H:i:s|after:start_time',
        ];

        if ($request->has('enable_overnight_booking') && $request->input('enable_overnight_booking')) {
            $rules['end_time'] = 'date_format:H:i:s';
        }

        if ($validate = $this->validationError($request, $rules)) {
            return $validate;
        }

        try {

            $data = $this->getBookingDetails($id);
            $rescheduleData = clone $data;

            //if ($rescheduleData->facility_booking_repeat_id != null) {
            // $repeatedFacilityId = $rescheduleData->facility_id;
            // $repeatDate = $rescheduleData->date;
            // $repeatStartTime = $rescheduleData->start_time;
            // $repeatEndTime = $rescheduleData->end_time;


            $currentProducts = FacilityBookingItem::where(['facility_booking_id' => $rescheduleData->id])
                ->whereNotIn('facility_booking_items.status_id', [2])
                ->join('order_items as oi', 'oi.id', '=', 'facility_booking_items.order_item_id')
                ->leftJoin('order_item_discounts as oid', 'oi.id', '=', 'oid.order_item_id')
                ->join('products as p', 'p.id', '=', 'oi.product_id')
                ->select(
                    'oi.id',
                    DB::raw('SUM(facility_booking_items.quantity) as quantity'),
                    DB::raw('SUM(oid.actual_price) as actual_price'),
                    DB::raw('SUM(facility_booking_items.price) as price'),
                    'p.tax_amount',
                    DB::raw('SUM(oid.actual_total) as actual_total'),
                    DB::raw('SUM(facility_booking_items.total) as total'),
                    'oi.product_id',
                    'p.name'
                )
                ->groupBy('oi.product_id')
                ->havingRaw('quantity > 0')
                ->get();
            // $currentProducts = FacilityBookingHelper::getProductWithDifferentTimingTemplates($repeatedFacilityId, $repeatStartTime, $repeatEndTime, [$repeatDate]);
            $currentOrderTotal = 0;
            $tax = 0;
            $price = 0;
            foreach ($currentProducts as $item) {
                $tax += ($item->tax_amount * $item->quantity);
                $price += $item->price;
                $currentOrderTotal += ($tax + $price);
                $item->rental = true;
            }
            unset($rescheduleData->order->items);
            $rescheduleData->order->items = $currentProducts;
            $rescheduleData->order->tax = $tax;
            $rescheduleData->order->price = $price;
            $rescheduleData->order->total = $currentOrderTotal;
            // }

            $date = $rescheduleData->date;
            if ($request->has('date')) {
                $date = $request->input('date');
            }

            $facilityId = $rescheduleData->facility_id;
            $rescheduleData->min_booking_time = Facility::where('id', $facilityId)->pluck('min_booking_time')->first();


            if ($request->has('facility_id')) {
                $facilityId = $request->input('facility_id');
            }

            $startTime = $rescheduleData->start_time;
            if ($request->has('start_time')) {
                $startTime = $request->input('start_time');
            }

            $endTime = $rescheduleData->end_time;
            if ($request->has('end_time')) {
                $endTime = $request->input('end_time');
            }

            $endDate = null;
            $rentals1 = [];
            $venueServiceId = $request->input('venue_service_id');

            $productTypeID = ProductType::where('name', 'Facility')->pluck('id')->first();
            $venueServiceConfiguration = VenueServiceConfiguration::firstOrCreate(['venue_service_id' => $venueServiceId, 'product_type_id' => $productTypeID]);


            $dayOfWeek = Carbon::parse($date)->englishDayOfWeek;
            $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first();

            $rescheduleData->product_combinations = [];

            if ($request->has('facility_id') && ($request->input('facility_id') != $rescheduleData->facility_id) || (Carbon::parse($date)->notEqualTo(Carbon::parse($rescheduleData->date)) || Carbon::parse($startTime)->notEqualTo(Carbon::parse($rescheduleData->start_time)) || Carbon::parse($endTime)->notEqualTo(Carbon::parse($rescheduleData->end_time)))) {

                $carbonEndTime = Carbon::parse($endTime);
                $carbonStartTime = Carbon::parse($startTime);
                if ($carbonEndTime->lt($carbonStartTime)) {
                    $endDate = Carbon::createFromFormat('Y-m-d', $date)->addDay()->toDateString();
                }
                if ($endDate) {
                    $endTime2 = $endTime;
                    $request1 = new Request(['date' => $request->input('date'), 'facility_id' => $facilityId, 'same_day' => 1]);
                    $facilityController = (new FacilityController());
                    $sameDayTiming = $facilityController->getFacilityTimings($request1);
                    if (!json_decode($sameDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get same day end time');
                    } else {
                        $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;
                    }

                    $request2 = new Request(['date' => $endDate, 'facility_id' => $facilityId, 'same_day' => 1]);
                    $nextDayTiming = $facilityController->getFacilityTimings($request2);
                    if (!json_decode($nextDayTiming->getContent())->status) {
                        throw new Exception('Couldn\'t get next day start time');
                    } else {
                        $startTime2 = json_decode($nextDayTiming->getContent())->start_time->start_time;
                    }
                }


                $duration = Carbon::parse($startTime)->diffInMinutes(Carbon::parse($endTime));
                if ($duration % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                    $duration += 1;
                }
                $rentals = FacilityBookingHelper::getRentalProducts($facilityId, $duration, $startTime, $endTime, $bitValue, $date);

                if ($endDate) {
                    $dayOfWeek = Carbon::parse($endDate)->englishDayOfWeek;
                    $bitValue = Weekday::where('name', $dayOfWeek)->pluck('bit_value')->first(); // Date bit value for rental checking (table weekdays)
                    $duration2 = Carbon::parse($startTime2)->diffInMinutes(Carbon::parse($endTime2)); //duration from given start & end time
                    if ($duration2 % 5 == 4) { // To avoid 11:59 issue where duration will be 1 min less
                        $duration2 += 1;
                    }
                    $rentals1 = FacilityBookingHelper::getRentalProducts($facilityId, $duration2, $startTime2, $endTime2, $bitValue, $endDate);
                    if (count($rentals1) > 0) {
                        $resultArray = [];
                        // Loop through each product in the first array
                        foreach ($rentals as $item1) {
                            // Loop through each product in the second array
                            foreach ($rentals1 as $item2) {
                                // Combine the products from both arrays
                                if (strtolower(gettype($item1['products'])) == 'array') {
                                    if (strtolower(gettype($item2['products'])) == 'array') {
                                        $combinedProducts = array_merge($item1['products'], $item2['products']);
                                    } else {
                                        $combinedProducts = array_merge($item1['products'], $item2['products']->toArray());
                                    }
                                } else {
                                    if (strtolower(gettype($item2['products'])) == 'array') {
                                        $combinedProducts = $item1['products']->merge(collect($item2['products']));
                                    } else {
                                        $combinedProducts = $item1['products']->merge($item2['products']);
                                    }
                                }
                                // Calculate the total price
                                $totalPrice = $item1['total_price'] + $item2['total_price'];
                                // Create the combined result
                                $combinedResult = [
                                    "total_price" => $totalPrice,
                                    "products" => $combinedProducts
                                ];
                                // Add the combined result to the result array
                                $resultArray[] = $combinedResult;

                                $resultArray = $this->reduceRedundantProducts($resultArray);
                                $rentals = $resultArray;
                            }
                        }
                    }
                }
                $rescheduleData->product_combinations = $rentals;
            }
            $rescheduleData->date = $date;
            $rescheduleData->end_time = (isset($endTime2) && $endTime2) ? $endTime2 : $endTime;
            $rescheduleData->start_time = $startTime;
            $rescheduleData->facility_id = (int)$facilityId;

            $increment = $venueServiceConfiguration->time_increment;
            $perCapacity = Facility::where('id', $facilityId)->pluck('per_capacity')->first();
            $facility = $this->getFacilityWithProducts($facilityId, $startTime, $endTime, $bitValue, $date);

            if ($facility && $facility->facilityRentals->count() > 0 || $perCapacity == 1) {

//                $rentalPackage = FacilityRental::where(['facility_id' => $facilityId, 'status_id' => 1])->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")->orderBy('start_time', 'asc')->get();

                $rentalPackage = FacilityRental::where('facility_id', $facilityId)
                    ->where('status_id', 1)
                    ->where(function($query) use ($bitValue, $date) {
                        // If is_seasonal is 1, add date range condition
                        $query->where(function($q) use ($bitValue,$date) {
                            $q->where('is_seasonal', 1)
                                ->whereRaw("(facility_rentals.weekdays & $bitValue) > 0")
                                ->whereDate('start_date', '<=', $date)
                                ->whereDate('end_date', '>=', $date);
                        })->orWhere(function($q) use ($bitValue) {
                            // If is_seasonal is 0, apply the regular condition
                            $q->where('is_seasonal', 0)->whereRaw("(facility_rentals.weekdays & $bitValue) > 0");
                        });
                    })
                    ->orderBy('start_time', 'asc')
                    ->get();

                if ($perCapacity == 1) {
                    $facilityStartTime = Carbon::parse($facility->opening_time);
                    $facilityEndTime = Carbon::parse($facility->closing_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                } else {
                    $facilityStartTime = Carbon::parse($rentalPackage->first()->start_time);
                    $facilityEndTime = Carbon::parse($rentalPackage->last()->end_time);
                    $bookings = FacilityBooking::where('start_time', '<=', $facilityStartTime)->where('start_time', '>=', $facilityEndTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();
                }

                $times = [];
                $currentBookingIndex = false;
                $bookingCount = $bookings->count();
                if ($bookingCount > 0) {
                    $currentBookingIndex = 0;
                    $currentBooking = $bookings[$currentBookingIndex];
                    $bookingStartTime = Carbon::parse($currentBooking->start_time);
                    $bookingEndTime = Carbon::parse($currentBooking->start_time);
                }

                $intervals = CarbonInterval::minutes($increment)->toPeriod($facilityStartTime, $facilityEndTime);
                foreach ($intervals as $time) {
                    if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                        if ($time->greaterThanOrEqualTo($bookingEndTime)) {
                            $currentBookingIndex += 1;
                            $currentBooking = $bookings[$currentBookingIndex];
                            $bookingStartTime = Carbon::parse($currentBooking->start_time);
                            $bookingEndTime = Carbon::parse($currentBooking->start_time);
                        }
                        if ($time->between($bookingStartTime, $bookingEndTime)) continue;
                    }
                    $times[] = (object)['time' => $time->format('H:i:s'), 'formatted' => $time->format('h:i a')];
                }
                if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                    if (!$facilityEndTime->between($bookingStartTime, $bookingEndTime)) {
                        $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                    }
                } else {
                    $times[] = (object)['time' => $facilityEndTime->format('H:i:s'), 'formatted' => $facilityEndTime->format('h:i a')];
                }
                $rescheduleData->times = $times;
            } else {
                $facilityController = (new FacilityController());
                $request1 = new Request(['date' => $date, 'facility_id' => $facilityId, 'same_day' => 1]);
                $sameDayTiming = $facilityController->getFacilityTimings($request1);
                if (!json_decode($sameDayTiming->getContent())->status) {
                    throw new Exception('Couldn\'t get same day timings');
                } else {
                    $startTime = json_decode($sameDayTiming->getContent())->start_time->start_time;
                    $endTime = json_decode($sameDayTiming->getContent())->end_time->end_time;

                    $bookings = FacilityBooking::where('start_time', '<=', $startTime)->where('start_time', '>=', $endTime)
                        ->where(['facility_id' => $facilityId, 'date' => $date])->where('id', '!=', $id)
                        ->orderBy('facility_bookings.start_time')->get();


                    $times = [];
                    $currentBookingIndex = false;

                    $bookingCount = $bookings->count();
                    if ($bookingCount > 0) {
                        $currentBookingIndex = 0;
                        $currentBooking = $bookings[$currentBookingIndex];
                        $bookingStartTime = Carbon::parse($currentBooking->start_time);
                        $bookingEndTime = Carbon::parse($currentBooking->start_time);
                    }

                    $intervals = CarbonInterval::minutes($increment)->toPeriod($startTime, $endTime);
                    foreach ($intervals as $time) {
                        if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                            if ($time->greaterThanOrEqualTo($bookingEndTime)) {
                                $currentBookingIndex += 1;
                                $currentBooking = $bookings[$currentBookingIndex];
                                $bookingStartTime = Carbon::parse($currentBooking->start_time);
                                $bookingEndTime = Carbon::parse($currentBooking->start_time);
                            }
                            if ($time->between($bookingStartTime, $bookingEndTime)) continue;
                        }
                        $times[] = (object)['time' => $time->format('H:i:s'), 'formatted' => $time->format('h:i a')];
                    }
                    if ($currentBookingIndex != false && $currentBookingIndex < $bookingCount) {
                        if (!$endTime->between($bookingStartTime, $bookingEndTime)) {
                            $times[] = (object)['time' => Carbon::parse($endTime)->format('H:i:s'), 'formatted' => Carbon::parse($endTime)->format('h:i a')];
                        }
                    } else {
                        $times[] = (object)['time' => Carbon::parse($endTime)->format('H:i:s'), 'formatted' => Carbon::parse($endTime)->format('h:i a')];
                    }
                    $rescheduleData->times = $times;
                }
            }

            $message = "Success";
            return response()->json(['status' => true, 'message' => $message, 'data' => $rescheduleData], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollback();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Get an facility booking details
     *
     * @param Integer $id
     * @return Object
     */
    private function getBookingDetails($id)
    {
        return FacilityBooking::with(['order' => function ($query) {
            $query->with([
                'items' => function ($query) {
                    $query->with(['rental', 'discount'])
                        ->join('products', 'products.id', 'order_items.product_id')
                        ->leftJoin('product_categories', 'products.id', 'product_categories.product_id')
                        ->select(
                            'products.name',
                            'products.price as product_price',
                            'products.product_type_id',
                            'product_categories.category_id',
                            'products.image',
                            'order_items.*',
                        )->where('order_items.status_id', '!=', 2)->where('product_categories.status_id', 1)->groupBy('order_items.id');
                }, 'companySale',
                'customer' => function ($query) {
                    $query->leftJoin('countries as ct', 'ct.id', 'customers.country_id')
                        ->join('customer_contacts as cc', 'cc.id', '=', 'customers.customer_contacts_id')
                        ->select('customers.*', 'ct.name as country', 'cc.mobile', 'cc.email');
                },
                'discount' => function ($query) {
                    $query->with(['promotion', 'member']);
                }
            ]);
        }, 'repeat'])->where(['facility_bookings.id' => $id])->first();
    }

    /**
     * Get an booking attendance
     *
     * @param Request $request
     * @param Integer $id
     * @return Response
     */
    public function getAttendance(Request $request)
    {
        try {

            $rules = [

                'order_id' => 'integer|required'
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }

            $participants = DB::table('orders as o')
                ->select(

                    DB::raw("CONCAT(c.first_name, if(c.last_name is not null, CONCAT(' ',c.last_name),'') ) as name"),
                    'c.profile_image',
                    'o.id as order_id'
                )
                ->leftJoin('group_customers as gc', function ($join) {
                    $join->on('gc.order_id', '=', 'o.id');
                    $join->on('gc.status_id', '=', DB::raw(1));
                })
                ->leftJoin('customers as c', 'c.id', '=', 'gc.customer_id')
                ->where('o.id', $request->input('order_id'))
                ->get();

            return response()->json(['status' => true, 'message' => 'Success', 'data' => $participants], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * Get an booking participants
     *
     * @param Request $request
     * @param Integer $id
     * @return Response
     */
    public function getBookedAttendance(Request $request)
    {
        try {
            $rules = [
                'start_time' => 'date_format:H:i:s',
                'end_time' => 'date_format:H:i:s|after:start_time',
                'date' => 'required|date_format:Y-m-d',
                'facility_id' => 'integer|required'
            ];

            if ($validate = $this->validationError($request, $rules)) {
                return $validate;
            }
            $is_golf_enabled = true;
            if ($request->has('venue_service_id') && $request->venue_service_id > 0) {
                $venueServiceConfiguration = VenueServiceConfiguration::where(['venue_service_id' => $request->venue_service_id, 'status_id' => 1])->first();
                if (!empty($venueServiceConfiguration)) {
                    if (!$venueServiceConfiguration->is_golf_enabled) {
                        $is_golf_enabled = false;
                    }
                }
            }
            $startTime = $request->input('start_time');
            $endTime = $request->input('end_time');
            $date = $request->input('date');
            $participants = DB::table('facility_bookings as fb')
                ->select(
                    'fb.id',
                    'fb.date',
                    'fb.start_time',
                    'fb.end_time',
                    'fb.order_id',
                    'fb.status_id',
                    'fb.facility_id',
                    'f.name as facility_name',
                    DB::raw("CONCAT(c.first_name, if(c.last_name is not null, CONCAT(' ',c.last_name),'') ) as name"),
                    'c.profile_image',
                    'o.id as order_id'
                )
                ->join('facilities as f', 'f.id', '=', 'fb.facility_id')
                ->join('orders as o', 'o.id', '=', 'fb.order_id')
                ->leftJoin('group_customers as gc', function ($join) use ($is_golf_enabled) {
                    if ($is_golf_enabled) {
                        $join->on('gc.facility_booking_id', '=', 'fb.id');
                    } else {
                        $join->on('gc.order_id', '=', 'o.id');
                    }
                    // $join->on('gc.order_id', '=', 'o.id');
                    $join->on('gc.status_id', '=', DB::raw(1));
                })
                ->leftJoin('customers as c', 'c.id', '=', 'gc.customer_id')
                ->where(function ($query) use ($startTime, $endTime) {
                    $query->where('fb.start_time', '=', $startTime)->orWhere('fb.end_time', '=', $endTime);
                    $query->orWhereRaw("'$startTime' between fb.start_time and fb.end_time and '$endTime' between fb.start_time and fb.end_time");
                })
                // ->where('fb.start_time', '>=', $startTime)->where('fb.end_time', '<=', $endTime)
                ->where('fb.status_id', '!=', 2)
                ->where('fb.facility_id', $request->input('facility_id'))->where('fb.date', '=', $date);
            // $log = Str::replaceArray('?', $participants->getBindings(), $participants->toSql());
            // Log::info('--------report log' . json_encode($log));
            $participants = $participants->groupBy('gc.id')->get();
            $orderDetails = (object)$participants;
            $data['venueLogo'] = $this->venue->profile_image != null ? config('filesystems.disks.azure.account_url') . '/' . $this->venue->profile_image : base_path() . '/resources/views/export/logo.png';
            if (isset($participants[0])) {
                $data['tee_time'] = $participants[0]->start_time;
            } else {
                $data['tee_time'] = 'NA';
            }
            $pdf = PDF::loadView('export/OrderAttendance', [
                'orderDetails' => $orderDetails,
                'data' => $data
            ])->setPaper('A5', 'landscape');;

            return $pdf->download('order_attendance.pdf');
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    /**
     * check in and out
     *
     * @param Request $request
     * @return Response
     */
    public function checkInAndOut($id)
    {
        DB::beginTransaction();
        try {
            $groupCustomer = GroupCustomer::find($id);
            $customer = Customer::with('customer_contact')->find($groupCustomer->customer_id);
            $order = Order::find($groupCustomer->order_id);
            $fbt = DB::table('facility_booking_tickets', 'fbt')
                ->join('order_items as oi', 'oi.id', '=', 'fbt.order_item_id')
                ->where('fbt.customer_id', $customer->id)
                ->where('oi.order_id', $order->id)
                ->select(DB::raw('fbt.*'))
                ->first();

            $productType = ProductType::where(['name' => 'Facility', 'status_id' => 1])->first();
            $timezone = $this->venue->timezone;
            if ($timezone == null)
                $timezone = 'Asia/Dubai';
            $currentDateTime = Carbon::now()->setTimezone($timezone)->format('Y-m-d H:i:s');

            if (!$groupCustomer) {
                return response()->json(['status' => false, 'message' => 'Data not found', 'data' => null], Response::HTTP_CONFLICT);
            }
            if ($groupCustomer->check_in_time) {
                if ($groupCustomer->check_out_time) {
                    return response()->json(['status' => false, 'message' => 'Cannot perform this action', 'data' => null], Response::HTTP_CONFLICT);
                } else {
                    $groupCustomer->check_out_time = now();
                    if ($fbt) {
                        DB::table('facility_booking_tickets')
                            ->where('id', $fbt->id) // Assuming 'id' is the primary key of the facility_booking_tickets table
                            ->update(['check_out_time' => now()]);

                        if ($productType) {
                            $scannerLog = ScannerLog::where([
                                'ticket_id' => $fbt->id,
                                'customer_id' => $fbt->customer_id, 'ticket_type' => 'facility_ticket'
                            ])->first();

                            if ($scannerLog) {
                                $scannerLog->check_out = now();
                                $scannerLog->updated_by = $this->userId;
                                $scannerLog->ticket_name = $fbt->ticket_code;
                                $scannerLog->save();
                            }
                        }
                    }
                    if ($this->venue->enable_email && $customer->customer_contact && $customer->customer_contact->email && PermissionHelper::venueHasPermissionForSubModule('mse', $this->venueId)) {
                        //                            $result = MailHelper::sendEmail($customer->customer_contact->email, new templateMails($customer, $this->venue, 'Survey', 'survey_email', $order, []));
                        $result = MailHelper::sendEmail($customer->customer_contact->email, new TemplateEmails($customer, $this->venue, 'Survey', 'survey_email', $order, []));
                    }
                }
            } else {
                $groupCustomer->check_in_time = now();
                if ($fbt) {
                    DB::table('facility_booking_tickets')
                        ->where('id', $fbt->id) // Assuming 'id' is the primary key of the facility_booking_tickets table
                        ->update(['check_in_time' => now()]);
                    if ($productType) {
                        $scannerLog = ScannerLog::where([
                            'ticket_id' => $fbt->id,
                            'customer_id' => $fbt->customer_id, 'ticket_type' => 'facility_ticket'
                        ])->first();
                        if (!$scannerLog)
                            $scannerLog = new ScannerLog;
                        $scannerLog->product_type_id = $productType->id;
                        $scannerLog->scanner_user_id = $this->userId;
                        $scannerLog->ticket_id = $fbt->id;
                        $scannerLog->customer_id = $fbt->customer_id;
                        $scannerLog->ticket_name = $fbt->ticket_code;
                        $scannerLog->ticket_type = 'facility_ticket';
                        $scannerLog->check_in = now();
                        $scannerLog->is_scanned = 0;
                        $scannerLog->status_id = 1;
                        $scannerLog->created_by = $this->userId;
                        $scannerLog->updated_by = $this->userId;
                        $scannerLog->save();
                    }
                }
                //                if ($this->venue->enable_custom_email && $this->venue->welcome_email) {
                //                    if ($this->venue->enable_email && $customer->customer_contact && $customer->customer_contact->email) {
                if ($this->venue->enable_email && $customer->customer_contact && $customer->customer_contact->email && PermissionHelper::venueHasPermissionForSubModule('mwm', $this->venueId)) {

                    //                        $result = MailHelper::sendEmail($customer->customer_contact->email, new templateMails($customer, $this->venue, 'Welcome', 'welcome_email', $order, []));
                    $result = MailHelper::sendEmail($customer->customer_contact->email, new TemplateEmails($customer, $this->venue, 'Welcome', 'welcome_email', $order, []));
                    info($result);
                }
                //                }
            }
            $groupCustomer->save();

            if (!$groupCustomer->check_in_and_out) {
                $groupCustomer->check_in_and_out = 'IN';
            } else if ($groupCustomer->check_in_and_out == 'IN') {
                $groupCustomer->check_in_and_out = 'OUT';
            } else if ($groupCustomer->check_in_and_out == 'OUT') {
                $groupCustomer->check_in_and_out = 'GONE';
            }
            $groupCustomer->save();

            // adding visits count in venue_customers
            if ((isset($scannerLog) && $scannerLog &&  $scannerLog->check_out == null) || isset($groupCustomer) && $groupCustomer &&  $groupCustomer->check_in_and_out == 'OUT') {
                $venueCustomer = VenueCustomer::where(['venue_id' => $this->venueId, 'customer_id' => $customer->id, 'status_id' => 1])->first();
                if ($venueCustomer) {
                    $venueCustomer->visits_count += 1;
                    $venueCustomer->save();
                }
            }

            DB::commit();
            return response()->json(['status' => true, 'message' => 'success'], Response::HTTP_OK);
        } catch (\Exception $e) {
            DB::rollBack();
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function voidCheckIn($id)
    {
        try {
            $groupCustomer = GroupCustomer::find($id);

            $customer = Customer::with('customer_contact')->find($groupCustomer->customer_id);
            $order = Order::find($groupCustomer->order_id);

            $fbt = DB::table('facility_booking_tickets', 'fbt')
                ->join('order_items as oi', 'oi.id', '=', 'fbt.order_item_id')
                ->where('fbt.customer_id', $customer->id)
                ->where('oi.order_id', $order->id)
                ->select(DB::raw('fbt.*'))
                ->first();

            if (!$groupCustomer) {
                return response()->json(['status' => false, 'message' => 'Data not found', 'data' => null], Response::HTTP_CONFLICT);
            }
            if ($groupCustomer->check_in_time) {
                if ($groupCustomer->check_out_time) {
                    return response()->json(['status' => false, 'message' => 'Cannot perform this action, Already checked out', 'data' => null], Response::HTTP_CONFLICT);
                } else {
                    if ($fbt) {
                        DB::table('facility_booking_tickets')
                            ->where('id', $fbt->id) // Assuming 'id' is the primary key of the facility_booking_tickets table
                            ->update(['check_in_time' => null]);
                        $scannerLog = ScannerLog::where([
                            'ticket_id' => $fbt->id,
                            'customer_id' => $fbt->customer_id, 'ticket_type' => 'facility_ticket'
                        ])->first();

                        if ($scannerLog) {
                            $scannerLog->status_id = 2;
                            $scannerLog->updated_by = $this->userId;
                            $scannerLog->ticket_name = $fbt->ticket_code;
                            $scannerLog->save();
                        }
                    }
                    $groupCustomer->check_in_time = null;
                }
            } else {
                return response()->json(['status' => false, 'message' => 'Cannot perform this action, not checked in', 'data' => null], Response::HTTP_CONFLICT);
            }
            $groupCustomer->check_in_and_out = 'IN';
            $groupCustomer->save();

            // adding visits count in venue_customers
            if (isset($groupCustomer) && $groupCustomer &&  $groupCustomer->check_in_time == null) {
                $venueCustomer = VenueCustomer::where(['venue_id' => $this->venueId, 'customer_id' => $customer->id, 'status_id' => 1])->first();
                if ($venueCustomer) {
                    $venueCustomer->visits_count -= 1;
                    $venueCustomer->save();
                }
            }
            return response()->json(['status' => true, 'message' => 'success'], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    public function bookingStatus($id, $status)
    {
        try {
            $message = '';
            $facilityBooking = FacilityBooking::find($id);
            if (!$facilityBooking) {
                throw new \Exception('Booking not found');
            }
            if($facilityBooking->is_booking_approved != 0){
                throw new \Exception('Cannot change status of this booking');
            }
            if ($status == 'approve') {
                if($this->venue->enable_cid){
                    $venue = $this->venue;
                    try {
                        //DB::beginTransaction();
                        $facilityBooking->is_booking_approved = 1;
                        $facilityBooking->is_cid_approved = 0;
                        $facilityBooking->save();
                        $api_key = VenueIntegrations::where(['venue_id' => $this->venueId])->first();
                        if (!$api_key) {
                            throw new \Exception('DCD credentials not configured');
                        }
                        $s3url = config('filesystems.disks.azure.account_url');
                        $customers = DB::table('facility_bookings as fb')
                            ->join('orders as o', 'o.id', '=', 'fb.order_id')
                            ->join('group_customers as gc', 'gc.order_id', '=', 'o.id')
                            ->join('customers as c', 'c.id', '=', 'gc.customer_id')
                            ->join('venue_customers as vc', function ($q) {
                                $q->on('vc.customer_id', '=', 'c.id')->where('vc.venue_id', $this->venueId);
                            })
                            ->join('customer_contacts as cc', 'cc.id', '=', 'c.customer_contacts_id')
                            ->leftJoin('customer_documents as ecd', function ($q) use ($venue) {
                                $q->on('ecd.customer_id', '=', 'c.id')->where('ecd.venue_id', $venue->id)->where('ecd.id_proof_type_id',32);
                            })->leftJoin('customer_documents as pcd', function ($q) use ($venue) {
                                $q->on('pcd.customer_id', '=', 'c.id')->where('pcd.venue_id', $venue->id)->where('pcd.id_proof_type_id',33);
                            })
//                            ->leftJoin('customer_documents as cd', function($q) use ($venue){
//                                $q->on('cd.customer_id', '=', 'c.id')->where('cd.venue_id', $venue->id);
//                            })
                            ->leftJoin('countries as cntry2', 'cntry2.id', '=', 'c.place_of_birth_id')
                            ->leftJoin('venue_customer_tags as vct', 'vct.venue_customer_id', '=', 'vc.id')
                            ->leftJoin('customer_configuration_tags as cct', 'cct.id', '=', 'vct.tag_id')
                            ->leftJoin('countries as cntry', 'cntry.id', '=', 'c.country_id')
                            ->where('fb.id', $id)
                            ->groupBy('gc.id')
                            ->select(
                                'c.id as customer_id',
                                DB::raw("IF(c.profile_image IS NOT NULL, CONCAT('$s3url/',c.profile_image),NULL) as profile_image"),
//                                'c.first_name as first_name',
                                'vc.customer_uuid as customer_uuid',
                                'vc.id as venue_customer_id',
                                'cntry.dcd_country_id as dcd_country_id',
                                'cntry.id as country_id',
                                'c.gender as gender',
                                'c.idn as idn',
                                'c.unifiedID as unifiedID',
                                DB::raw("CONCAT(c.first_name, CONCAT(if(c.last_name is not null,' ',''), COALESCE(c.last_name,''))) AS first_name"),
                                'ecd.id_proof_type_id as id_proof_type_id',
                                'ecd.id_proof_number as id_proof_number',
                                'ecd.issue_date as issue_date',
                                'ecd.expiry_date as expiry_date',
                                'pcd.id_proof_type_id as passport_id_proof_type_id',
                                'pcd.id_proof_number as passport_id_proof_number',
                                'pcd.issue_date as passport_issue_date',
                                'pcd.expiry_date as passport_expiry_date',
//                                'cd.id_proof_type_id as id_proof_type_id',
//                                'cd.id_proof_number as id_proof_number',
//                                'cd.issue_date as issue_date',
//                                'cd.expiry_date as expiry_date',
                                'cntry2.dcd_country_id as place_of_birth_id',
                                'c.religion as religion',
                                'c.religion_id as religion_id',
                                'c.dob as dob',
                                'cc.mobile as mobile',
                                'cc.email as email',
                                'fb.id as facility_booking_id',
                                'fb.is_auto_approved as is_auto_approved',
                                'gc.id as group_customer_id',
                                'o.id as order_id',
                                'o.venue_id as venue_id',
                                'vc.is_verified as is_verified',
                                'vc.dcd_person_id as dcd_person_id',
                                DB::raw("date_format(CONVERT_TZ(fb.created_at, '+00:00', '+04:00'), '%Y-%m-%d %H:%i:%s') as booking_created_at"),
                                "fb.date as visit_date",
                                "fb.start_time as visit_time",
                                'cct.name as tag_name',
                                'vc.uae_resident as uae_resident'
                            )
                            ->get();
                        foreach ($customers as $customer) {
                            DB::beginTransaction();
                            if (!$customer->customer_uuid) {
                                $uuid = $this->getUuid();
                                VenueCustomer::where(['id' => $customer->venue_customer_id])->update(['customer_uuid' => $uuid]);
                            } else {
                                $uuid = $customer->customer_uuid;
                            }

                            $customerObj = DCDService::makeCustomer($customer, $uuid);

                            info('Data sending to CID '.json_encode($customerObj));

//                            $result = $this->checkRequiredFields($customerObj);
//
//                            if($result){
                                $cid_status = DCDService::submitCIDData($api_key, $customerObj, $customer);
                                if(!$cid_status){
                                    $facilityBooking->is_booking_approved = 0;
                                    $facilityBooking->is_cid_approved = null;
                                    $facilityBooking->save();

//                                    $orderController = new OrderController();
//                                    $request = new Request([
//                                        'type'=>'rejection',
//                                        'rejection_reason_type'=>"Rejected",
//                                        'rejection_reason'=>" due to Missing data for {$customer->first_name}"
//                                    ]);
//                                    $orderController->cancel($customer->order_id,$request);
                                }
//                            }else{
//                                info('Data missing, not sent to CID '.json_encode($customerObj));
//                            }
                            DB::commit();
                        }
                    } catch (\Exception $e) {
                        DB::rollBack();
                        Log::error('error in approving booking');
                        Log::error(json_encode($e->getLine()));
                        Log::error(json_encode($e->getMessage()));
                        Log::error(json_encode($e->getTraceAsString()));
                    }

                }
                else{
                    $facilityBooking->is_booking_approved = 1;
                    $facilityBooking->save();
                    $message = 'Successfully approved';

                    $booking = DB::table('facilities', 'f')
                        ->join('facility_bookings as fb', 'fb.facility_id', '=', 'f.id')
                        ->join('orders as o', 'o.id', '=', 'fb.order_id')
                        ->join('customers as c', 'c.id', '=', 'o.customer_id')
                        ->join('customer_contacts as cc', 'cc.id', '=', 'c.customer_contacts_id')
                        ->select(
                            'f.name as facility_name',
                            'fb.date as booking_date',
                            'fb.start_time as start_time',
                            DB::raw("CONCAT(c.first_name, CONCAT(if(c.last_name is not null,' ',''), COALESCE(c.last_name,''))) as customer_name"),
                            'cc.email as email',
                            'o.venue_id as venue_id',
                        )
                        ->where('fb.id', $id)
                        ->first();

                    if ($booking && $this->venue->enable_email && PermissionHelper::venueHasPermissionForSubModule('bat', $this->venueId)) {
                        SendFacilityBookingApprovalMailJob::dispatch($facilityBooking,$this->venue);
                    }
                }
            }

            return response()->json(['status' => true, 'message' => $message], Response::HTTP_OK);
        } catch (\Exception $e) {
            return response()->json(['status' => false, 'message' => $this->getError($e), 'data' => null], Response::HTTP_CONFLICT);
        }
    }

    function checkRequiredFields($data) {
        // List of required keys
        $requiredKeys = [
            'nationality',
            'gender',
            'firstName',
            //'religion',
            'dob',
            'phoneNumber',
            'email',
            'visitDate',
            'visitTime',
//            'visitPurpose',
            'qodeReferenceNumber'
        ];

        // Check if any required key is missing or has a null value
        foreach ($requiredKeys as $key) {
            if (!isset($data[$key]) || $data[$key] === null) {
                // Return the key that is missing or has a null value
                return false;
            }
        }

        // If everything is okay, return true
        return true;
    }

    public function getUuid()
    {
        $uuid = (string) Str::uuid();

        if(VenueCustomer::where('customer_uuid', $uuid)->exists()) {
            // If it exists, recursively call the function to generate a new UUID
            return $this->getUuid();
        }

        // Return the unique UUID
        return $uuid;
    }

    public function autoCheckInKiosk($invoiceId)
    {
        $inv = Invoice::with(['orders.items', 'orders.group_customers'])->find($invoiceId);
        if ($inv) {
            foreach ($inv->orders as $order) {
                foreach ($order->items as $oItem) {
                    $fbts = FacilityBookingTicket::where('order_item_id', $oItem->id)->get();
                    if (count($fbts)) {
                        foreach ($fbts as $fbt) {
                            $fbt->check_in_time = now();
                            $fbt->save();
                            $scannerLog = new ScannerLog();
                            $scannerLog->product_type_id = 6;
                            $scannerLog->ticket_id = $fbt->id;
                            $scannerLog->customer_id = $fbt->customer_id;
                            $scannerLog->ticket_type = 'facility_ticket';
                            $scannerLog->ticket_name = $fbt->ticket_code;
                            $scannerLog->scanner_user_id = $this->userId;
                            $scannerLog->check_in = now();
                            $scannerLog->is_scanned = 0;
                            $scannerLog->save();

                            if ((isset($scannerLog) && $scannerLog)) {
                                $venueCustomer = VenueCustomer::where(['venue_id' => $this->venueId, 'customer_id' => $fbt->customer_id, 'status_id' => 1])->first();
                                if ($venueCustomer) {
                                    $venueCustomer->visits_count += 1;
                                    $venueCustomer->save();
                                }
                            }
                        }
                    }
                }
                foreach ($order->group_customers as $gc) {
                    $gc->check_in_and_out = "OUT";
                    $gc->check_in_time = now();
                    $gc->save();
                }
            }
        }
    }
    public function validateOpenBaseProduct($request)
    {
        if ($this->hasOpenProduct($request->products)) {
            $openRentalCount = collect($request->products)->where('product_id', 0)->where('rental', 'true')->count();
            $totalRepeatDays = 0;
            if ($this->hasRentalProduct($request->products)) {
                throw new \Exception('Cannot book open products with normal products');
            } else if ($openRentalCount != 1) {
                throw new \Exception('Only one open base product allowed');
            } else {
                $openProductQty = collect($request->products)->where('product_id', 0)->where('rental', 'true')->sum('quantity');
                foreach ($request->repeats as $repeat) {
                    $totalRepeatDays += count($repeat['selected_dates']);
                }
                if ($totalRepeatDays != $openProductQty) {
                    throw new \Exception("Open product quantity should be match with number of booking dates, Quantity should be ($totalRepeatDays)");
                }
            }
        }
    }
}
